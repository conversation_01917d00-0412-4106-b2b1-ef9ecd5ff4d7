"""
Google Search API integration for ground truth validation.
Uses actual Google Custom Search API to get real search results.
"""

import os
import sys
import json
import time
import requests
from pathlib import Path
from typing import List, Dict, Any, Optional
from urllib.parse import quote_plus

# Add src to path for imports
src_path = Path(__file__).parent.parent
if str(src_path) not in sys.path:
    sys.path.insert(0, str(src_path))

from validation.ground_truth_validator import (
    GroundTruthValidator, SearchResult, GroundTruthData, ValidationScore
)


class GoogleSearchValidator(GroundTruthValidator):
    """Enhanced validator using real Google Custom Search API."""
    
    def __init__(self, api_key: str = None, search_engine_id: str = None, max_results: int = 10):
        """
        Initialize with Google API credentials.
        
        Args:
            api_key: Google Custom Search API key
            search_engine_id: Custom Search Engine ID
            max_results: Maximum number of results to fetch
        """
        super().__init__(max_results)
        
        # Get API credentials from environment or parameters
        self.api_key = api_key or os.getenv('GOOGLE_API_KEY')
        self.search_engine_id = search_engine_id or os.getenv('GOOGLE_SEARCH_ENGINE_ID')
        
        if not self.api_key or not self.search_engine_id:
            print("⚠️  Warning: Google API credentials not found.")
            print("   Set GOOGLE_API_KEY and GOOGLE_SEARCH_ENGINE_ID environment variables")
            print("   or pass them as parameters to use real Google search.")
            print("   Falling back to simulated search results.")
            self.use_real_api = False
        else:
            self.use_real_api = True
            print("✅ Google Custom Search API configured")
    
    def search_google(self, query: str, num_results: int = None) -> List[SearchResult]:
        """
        Search Google using Custom Search API.
        
        Args:
            query: Search query
            num_results: Number of results to return
            
        Returns:
            List of search results
        """
        if num_results is None:
            num_results = self.max_results
        
        print(f"🔍 Searching Google for: '{query}' (top {num_results} results)")
        
        if not self.use_real_api:
            print("📝 Using simulated results (API not configured)")
            return super().search_google(query, num_results)
        
        try:
            # Google Custom Search API endpoint
            url = "https://www.googleapis.com/customsearch/v1"
            
            params = {
                'key': self.api_key,
                'cx': self.search_engine_id,
                'q': query,
                'num': min(num_results, 10),  # API limit is 10 per request
                'safe': 'active'
            }
            
            print("🌐 Making API request to Google...")
            response = requests.get(url, params=params, timeout=10)
            response.raise_for_status()
            
            data = response.json()
            
            if 'items' not in data:
                print("⚠️  No search results found")
                return []
            
            results = []
            for i, item in enumerate(data['items']):
                result = SearchResult(
                    title=item.get('title', ''),
                    url=item.get('link', ''),
                    snippet=item.get('snippet', ''),
                    position=i + 1
                )
                results.append(result)
            
            print(f"✅ Retrieved {len(results)} real search results")
            return results
            
        except requests.exceptions.RequestException as e:
            print(f"❌ API request failed: {e}")
            print("📝 Falling back to simulated results")
            return super().search_google(query, num_results)
        
        except Exception as e:
            print(f"❌ Unexpected error: {e}")
            print("📝 Falling back to simulated results")
            return super().search_google(query, num_results)
    
    def extract_ground_truth(self, search_results: List[SearchResult], query: str) -> GroundTruthData:
        """
        Enhanced ground truth extraction with better NLP.
        
        Args:
            search_results: List of search results
            query: Original search query
            
        Returns:
            Ground truth data with extracted insights
        """
        print("📊 Extracting ground truth from search results...")
        
        # Combine all text from search results
        all_text = ""
        for result in search_results:
            all_text += f"{result.title} {result.snippet} "
        
        all_text = all_text.lower()
        
        # Enhanced keyword extraction
        key_concepts = set()
        common_themes = set()
        expert_recommendations = []
        
        # Performance-related concepts
        performance_keywords = [
            'optimization', 'performance', 'speed', 'fast', 'efficient', 'cache', 'caching',
            'minify', 'compress', 'lazy loading', 'cdn', 'bundle', 'splitting'
        ]
        
        # Implementation themes
        implementation_keywords = [
            'best practices', 'implementation', 'strategy', 'approach', 'method',
            'technique', 'solution', 'framework', 'tool', 'library'
        ]
        
        # Extract concepts
        for keyword in performance_keywords:
            if keyword in all_text:
                key_concepts.add(keyword)
        
        for keyword in implementation_keywords:
            if keyword in all_text:
                common_themes.add(keyword)
        
        # Extract recommendations from titles and snippets
        for result in search_results:
            text = f"{result.title} {result.snippet}"
            
            # Look for recommendation patterns
            if any(word in text.lower() for word in ['should', 'recommend', 'best', 'important', 'essential']):
                # Extract the sentence containing the recommendation
                sentences = text.split('.')
                for sentence in sentences:
                    if any(word in sentence.lower() for word in ['should', 'recommend', 'best']):
                        expert_recommendations.append(sentence.strip())
                        break
        
        # Remove duplicates and limit recommendations
        expert_recommendations = list(set(expert_recommendations))[:10]
        
        ground_truth = GroundTruthData(
            query=query,
            search_results=search_results,
            key_concepts=list(key_concepts),
            common_themes=list(common_themes),
            expert_recommendations=expert_recommendations,
            timestamp=time.strftime("%Y-%m-%d %H:%M:%S")
        )
        
        print(f"✅ Extracted {len(key_concepts)} concepts, {len(common_themes)} themes, {len(expert_recommendations)} recommendations")
        return ground_truth
    
    def validate_ideas(self, ideas, ground_truth: GroundTruthData) -> ValidationScore:
        """
        Enhanced validation with better scoring algorithm.
        
        Args:
            ideas: CoT-generated ideas
            ground_truth: Ground truth data from search results
            
        Returns:
            Enhanced validation score
        """
        print("🔍 Validating CoT ideas against ground truth...")
        
        # Extract all text from CoT ideas
        cot_text = f"{ideas.topic} {ideas.summary}"
        bullet_text = " ".join([bp.content for bp in ideas.bullet_points])
        thinking_text = " ".join([step.thought for step in ideas.thinking_chain])
        full_cot_text = f"{cot_text} {bullet_text} {thinking_text}".lower()
        
        # Calculate concept overlap with fuzzy matching
        concept_matches = 0
        for concept in ground_truth.key_concepts:
            if concept in full_cot_text or any(concept in bp.content.lower() for bp in ideas.bullet_points):
                concept_matches += 1
        
        concept_overlap = concept_matches / max(len(ground_truth.key_concepts), 1)
        
        # Calculate theme alignment
        theme_matches = 0
        for theme in ground_truth.common_themes:
            if theme in full_cot_text:
                theme_matches += 1
        
        theme_alignment = theme_matches / max(len(ground_truth.common_themes), 1)
        
        # Enhanced recommendation quality scoring
        high_priority_items = len([bp for bp in ideas.bullet_points if bp.priority == "high"])
        actionable_items = len([bp for bp in ideas.bullet_points if len(bp.supporting_evidence) > 0])
        detailed_items = len([bp for bp in ideas.bullet_points if len(bp.content) > 50])
        
        recommendation_quality = min((high_priority_items * 0.4 + actionable_items * 0.4 + detailed_items * 0.2) / 5.0, 1.0)
        
        # Enhanced novelty scoring
        gt_text = " ".join([r.snippet for r in ground_truth.search_results]).lower()
        novel_items = 0
        for bp in ideas.bullet_points:
            # Check if the core concept is novel
            bp_words = set(bp.content.lower().split())
            gt_words = set(gt_text.split())
            
            # If less than 30% word overlap, consider it novel
            overlap_ratio = len(bp_words.intersection(gt_words)) / len(bp_words) if bp_words else 0
            if overlap_ratio < 0.3:
                novel_items += 1
        
        novelty_score = novel_items / len(ideas.bullet_points) if ideas.bullet_points else 0.0
        
        # Weighted overall score
        overall_score = (
            concept_overlap * 0.35 +      # Increased weight for concept matching
            theme_alignment * 0.25 +
            recommendation_quality * 0.25 +
            novelty_score * 0.15           # Reduced weight for novelty
        )
        
        # Generate detailed feedback
        feedback = f"""
Validation Metrics:
- Concept Overlap: {concept_overlap:.2f} ({concept_matches}/{len(ground_truth.key_concepts)} concepts matched)
- Theme Alignment: {theme_alignment:.2f} ({theme_matches}/{len(ground_truth.common_themes)} themes matched)
- Recommendation Quality: {recommendation_quality:.2f} (H:{high_priority_items}, A:{actionable_items}, D:{detailed_items})
- Novelty Score: {novelty_score:.2f} ({novel_items}/{len(ideas.bullet_points)} novel items)

Matched Concepts: {[c for c in ground_truth.key_concepts if c in full_cot_text]}
Ground Truth Key Concepts: {ground_truth.key_concepts}
Ground Truth Themes: {ground_truth.common_themes}

Quality Indicators:
- High Priority Items: {high_priority_items}
- Items with Evidence: {actionable_items}
- Detailed Items (>50 chars): {detailed_items}
- Total Bullet Points: {len(ideas.bullet_points)}
        """
        
        validation_score = ValidationScore(
            concept_overlap=concept_overlap,
            theme_alignment=theme_alignment,
            recommendation_quality=recommendation_quality,
            novelty_score=novelty_score,
            overall_score=overall_score,
            detailed_feedback=feedback.strip()
        )
        
        print(f"✅ Enhanced validation complete - Overall Score: {overall_score:.2f}")
        return validation_score


def setup_google_api():
    """Helper function to set up Google API credentials."""
    print("🔧 Google Custom Search API Setup")
    print("=" * 40)
    print("To use real Google search results, you need:")
    print("1. Google Custom Search API key")
    print("2. Custom Search Engine ID")
    print()
    print("Get them from:")
    print("- API Key: https://developers.google.com/custom-search/v1/introduction")
    print("- Search Engine: https://cse.google.com/cse/")
    print()
    print("Set environment variables:")
    print("export GOOGLE_API_KEY='your-api-key'")
    print("export GOOGLE_SEARCH_ENGINE_ID='your-search-engine-id'")
    print()


def main():
    """Main function for testing the Google search validator."""
    print("🧪 Testing Google Search Ground Truth Validator")
    print("=" * 50)
    
    # Check if API is configured
    if not os.getenv('GOOGLE_API_KEY'):
        setup_google_api()
        print("📝 Proceeding with simulated results for demo...")
        print()
    
    validator = GoogleSearchValidator(max_results=5)
    
    # Test validation
    topic = "web application performance optimization"
    context = "frontend techniques and best practices"
    
    try:
        ideas, ground_truth, score = validator.run_validation(topic, context)
        
        print("\n" + "="*60)
        print("📊 GROUND TRUTH VALIDATION RESULTS")
        print("="*60)
        print(f"🎯 Overall Score: {score.overall_score:.2f}")
        print(f"📊 Concept Overlap: {score.concept_overlap:.2f}")
        print(f"🎨 Theme Alignment: {score.theme_alignment:.2f}")
        print(f"⭐ Recommendation Quality: {score.recommendation_quality:.2f}")
        print(f"💡 Novelty Score: {score.novelty_score:.2f}")
        print("\n📋 Detailed Feedback:")
        print(score.detailed_feedback)
        
        # Save results
        validator.save_validation_results(
            ideas, ground_truth, score,
            "results/ground_truth_validation/google_search_validation.json"
        )
        
        print(f"\n🎉 Validation complete! Overall score: {score.overall_score:.2f}")
        
    except Exception as e:
        print(f"❌ Validation failed: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
