# Import Path Update Guide

After reorganizing the repository, you'll need to update import statements in your code.

## Old → New Import Paths

### Core Components
```python
# Old
from cot_idea_generator import CoTIdeaSystem
from evaluation import style_score
from models.style_extraction import StyleAnalysis

# New
from src.cot_system.cot_idea_generator import CoTIdeaSystem
from src.dspy_pipeline.evaluation.evaluation import style_score
from src.dspy_pipeline.models.style_extraction import StyleAnalysis
```

### Web Application
```python
# Old
from app import app

# New
from src.web.app import app
```

### Utilities
```python
# Old
from utils.data_utils import load_examples
from utils.oauth_client import get_oauth_client

# New
from src.dspy_pipeline.utils.data_utils import load_examples
from src.dspy_pipeline.utils.oauth_client import get_oauth_client
```

## Running the Application

### Web Application
```bash
# From repository root
python -m src.web.app

# Or use the setup script
python scripts/setup/setup_and_run.py
```

### DSPy Pipeline
```bash
# From repository root
python -m src.dspy_pipeline.main
```

### Tests
```bash
# Unit tests
python -m pytest tests/unit/

# Integration tests
python -m pytest tests/integration/

# Web tests
python tests/web/test_web_api.py
```

## Quick Fix Script

If you encounter import errors, you can create a quick fix by adding the src directory to your Python path:

```python
import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent / 'src'))

# Now you can import directly
from cot_system.cot_idea_generator import CoTIdeaSystem
from web.app import app
```
