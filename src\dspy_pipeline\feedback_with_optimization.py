"""
Complete feedback optimization pipeline.
1. Takes style examples
2. Generates images from them
3. Analyzes generated images
4. Uses feedback to optimize the DSPy predictor
5. Tests the optimized predictor
"""

import asyncio
import json
import os
from datetime import datetime
import dspy
from dspy import Example

# Import our modules
from main import setup_dspy, create_predictor, optimize_predictor_labeled_fewshot
from utils.data_utils import load_examples
from simple_feedback_test import generate_image_from_style, analyze_generated_image, calculate_style_similarity


class FeedbackOptimizer:
    """Handles the complete feedback optimization pipeline."""
    
    def __init__(self):
        self.original_predictor = None
        self.optimized_predictor = None
        self.feedback_data = []
    
    def setup(self):
        """Setup DSPy and create original predictor."""
        setup_dspy()
        lm = dspy.LM("openai/gpt-4o-mini", cache_in_memory=False)
        dspy.configure(lm=lm)
        self.original_predictor = create_predictor()
        print("✅ DSPy setup complete")
    
    async def collect_feedback_data(self, num_examples: int = 10) -> list:
        """Collect feedback data by generating and analyzing images."""
        print(f"\n📊 Collecting feedback data from {num_examples} examples...")
        
        # Load examples
        examples_file = "examples/groovjones_styles/tile_examples.jsonl"
        examples = load_examples(examples_file)
        test_examples = examples[:num_examples]
        
        # Create output directory
        output_dir = "feedback_optimization"
        os.makedirs(output_dir, exist_ok=True)
        
        feedback_results = []
        
        for i, example in enumerate(test_examples, 1):
            try:
                print(f"\n🔄 Processing example {i}/{len(test_examples)}")
                
                # Extract data
                original_instruction = example["instruction"]
                expected_style = example["style"]
                expected_metadata = example["metadata"]
                
                # Get style name
                metadata = json.loads(expected_metadata)
                style_name = metadata.get("style_name", f"style_{i}").replace(" ", "_")
                
                print(f"   Style: {style_name}")
                
                # Generate image from expected style
                timestamp = datetime.now().strftime("%H%M%S")
                image_path = os.path.join(output_dir, f"feedback_{style_name}_{timestamp}.png")
                
                await generate_image_from_style(expected_style, image_path)
                
                # Analyze generated image with original predictor
                actual_analysis = await analyze_generated_image(self.original_predictor, image_path)
                
                # Calculate similarity
                similarity = calculate_style_similarity(expected_style, actual_analysis["style"])
                
                print(f"   Similarity: {similarity['overall_score']:.3f}")
                
                # Store feedback data
                feedback_item = {
                    "instruction": original_instruction,
                    "expected_style": expected_style,
                    "actual_style": actual_analysis["style"],
                    "similarity_score": similarity["overall_score"],
                    "style_name": style_name,
                    "image_path": image_path
                }
                
                feedback_results.append(feedback_item)
                self.feedback_data.append(feedback_item)
                
            except Exception as e:
                print(f"   ❌ Error processing example {i}: {e}")
                continue
        
        print(f"\n📈 Collected {len(feedback_results)} feedback items")
        avg_similarity = sum(item["similarity_score"] for item in feedback_results) / len(feedback_results)
        print(f"   Average similarity before optimization: {avg_similarity:.3f}")
        
        return feedback_results
    
    def create_optimization_examples(self) -> list:
        """Create DSPy training examples from feedback data."""
        print(f"\n🎯 Creating optimization examples from feedback...")
        
        optimization_examples = []
        
        for feedback in self.feedback_data:
            # Create example that teaches the model the expected output
            example = Example(
                instruction=feedback["instruction"],
                style=feedback["expected_style"]  # This is what we want the model to produce
            ).with_inputs('instruction')
            
            optimization_examples.append(example)
        
        print(f"   Created {len(optimization_examples)} optimization examples")
        return optimization_examples
    
    async def optimize_predictor(self, optimization_examples: list):
        """Optimize the predictor using feedback examples."""
        print(f"\n⚡ Optimizing predictor with {len(optimization_examples)} examples...")
        
        try:
            # Use LabeledFewShot optimization
            self.optimized_predictor = optimize_predictor_labeled_fewshot(
                self.original_predictor,
                optimization_examples,
                k=len(optimization_examples)  # Use all examples
            )
            print("✅ Predictor optimization complete")
            
        except Exception as e:
            print(f"❌ Error optimizing predictor: {e}")
            self.optimized_predictor = self.original_predictor  # Fallback
    
    async def test_optimized_predictor(self) -> dict:
        """Test the optimized predictor on new images."""
        print(f"\n🧪 Testing optimized predictor...")
        
        if not self.optimized_predictor:
            print("❌ No optimized predictor available")
            return {}
        
        # Test on the same images but with optimized predictor
        test_results = []
        
        for i, feedback in enumerate(self.feedback_data):
            try:
                print(f"   Testing {feedback['style_name']}...")
                
                # Analyze the same generated image with optimized predictor
                optimized_analysis = await analyze_generated_image(
                    self.optimized_predictor, 
                    feedback["image_path"]
                )
                
                # Calculate new similarity
                new_similarity = calculate_style_similarity(
                    feedback["expected_style"], 
                    optimized_analysis["style"]
                )
                
                test_result = {
                    "style_name": feedback["style_name"],
                    "original_similarity": feedback["similarity_score"],
                    "optimized_similarity": new_similarity["overall_score"],
                    "improvement": new_similarity["overall_score"] - feedback["similarity_score"]
                }
                
                test_results.append(test_result)
                
                print(f"      Original: {test_result['original_similarity']:.3f}")
                print(f"      Optimized: {test_result['optimized_similarity']:.3f}")
                print(f"      Improvement: {test_result['improvement']:+.3f}")
                
            except Exception as e:
                print(f"   ❌ Error testing {feedback['style_name']}: {e}")
                continue
        
        # Calculate overall improvement
        if test_results:
            avg_original = sum(r["original_similarity"] for r in test_results) / len(test_results)
            avg_optimized = sum(r["optimized_similarity"] for r in test_results) / len(test_results)
            avg_improvement = avg_optimized - avg_original
            
            summary = {
                "test_results": test_results,
                "average_original": avg_original,
                "average_optimized": avg_optimized,
                "average_improvement": avg_improvement,
                "num_tests": len(test_results)
            }
            
            print(f"\n📊 Optimization Summary:")
            print(f"   Average similarity before: {avg_original:.3f}")
            print(f"   Average similarity after:  {avg_optimized:.3f}")
            print(f"   Average improvement:       {avg_improvement:+.3f}")
            
            return summary
        
        return {}
    
    async def run_complete_pipeline(self, num_examples: int = 10):
        """Run the complete feedback optimization pipeline."""
        print("🚀 Starting Complete Feedback Optimization Pipeline")
        print("=" * 60)
        
        try:
            # Step 1: Setup
            self.setup()
            
            # Step 2: Collect feedback data
            feedback_data = await self.collect_feedback_data(num_examples)
            
            if not feedback_data:
                print("❌ No feedback data collected")
                return
            
            # Step 3: Create optimization examples
            opt_examples = self.create_optimization_examples()
            
            # Step 4: Optimize predictor
            await self.optimize_predictor(opt_examples)
            
            # Step 5: Test optimized predictor
            test_summary = await self.test_optimized_predictor()
            
            # Step 6: Save results
            output_dir = "feedback_optimization"
            results_file = os.path.join(output_dir, f"optimization_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json")
            
            results = {
                "feedback_data": feedback_data,
                "test_summary": test_summary,
                "timestamp": datetime.now().isoformat()
            }
            
            with open(results_file, 'w') as f:
                json.dump(results, f, indent=2)
            
            print(f"\n📁 Complete results saved to: {results_file}")
            print("✅ Pipeline completed successfully!")
            
            return results
            
        except Exception as e:
            print(f"❌ Pipeline error: {e}")
            import traceback
            traceback.print_exc()


async def main():
    """Main function to run the complete pipeline."""
    optimizer = FeedbackOptimizer()
    await optimizer.run_complete_pipeline(num_examples=2)


if __name__ == "__main__":
    asyncio.run(main())
