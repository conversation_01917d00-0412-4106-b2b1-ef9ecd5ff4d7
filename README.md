# DSPy Chain of Thought Idea Generator

A comprehensive system for generating structured ideas using DSPy's Chain of Thought reasoning, with web interface and feedback optimization.

## 🏗️ Project Structure

```
├── src/                          # Source code
│   ├── cot_system/              # Chain of Thought core system
│   ├── web/                     # Web application (Flask + HTML/CSS/JS)
│   └── dspy_pipeline/           # DSPy optimization pipeline
├── tests/                       # Test suites
│   ├── unit/                    # Unit tests
│   ├── integration/             # Integration tests
│   └── web/                     # Web application tests
├── scripts/                     # Utility scripts
│   ├── setup/                   # Setup and deployment scripts
│   └── data_processing/         # Data processing utilities
├── data/                        # Data files and examples
├── results/                     # Generated results and outputs
├── docs/                        # Documentation
└── demos/                       # Demo files and examples
```

## 🚀 Quick Start

1. **Setup**: `python scripts/setup/setup_and_run.py`
2. **Web App**: Open http://localhost:5000
3. **Generate Ideas**: Enter topic, choose template, get structured ideas!

## 📚 Documentation

- [Deployment Guide](docs/DEPLOYMENT_GUIDE.md)
- [Web Frontend Guide](docs/WEB_FRONTEND_README.md)
- [Async Implementation](docs/ASYNC_IMPLEMENTATION.md)
- [Feedback Optimization](docs/FEEDBACK_OPTIMIZATION_README.md)

## 🧪 Testing

- **Unit Tests**: `python -m pytest tests/unit/`
- **Integration Tests**: `python -m pytest tests/integration/`
- **Web Tests**: `python tests/web/test_web_api.py`

## 🎯 Features

- **Chain of Thought Reasoning**: Step-by-step AI thinking process
- **Web Interface**: Beautiful, responsive web application
- **Template System**: 8 specialized contexts for different domains
- **Feedback Optimization**: Image generation and feedback loops
- **DSPy Integration**: Advanced optimization techniques
- **Async Support**: High-performance async operations

Built with DSPy, Flask, and modern web technologies.
