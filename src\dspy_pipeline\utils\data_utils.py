"""
Data handling utilities for style extraction pipeline.
"""

import json
import dspy
import asyncio
from dspy import Example


def gen_train_dev_data(dataset: dict, train: int = 10, dev: int = 70) -> tuple[list[Example], list[Example]]:
    """
    Generate training and development datasets from loaded examples.
    
    Args:
        dataset: List of example dictionaries
        train: Number of examples for training set
        dev: Number of examples for development set
        
    Returns:
        Tuple of (trainset, devset) as lists of dspy.Example objects
    """
    trainset, devset = [], []

    for i, ex in enumerate(dataset):
        example = dspy.Example(
            instruction=ex["instruction"], 
            style=ex["style"], 
            metadata=ex["metadata"]
        ).with_inputs('instruction')

        if i < train:
            trainset.append(example)
        elif i < dev:
            devset.append(example)
        else:
            break
    return trainset, devset


def get_images_links(file: str) -> list[str]:
    """
    Extract image links from a JSON file.
    
    Args:
        file: Path to JSON file containing image data
        
    Returns:
        List of image URLs
    """
    with open(file, "r") as f:
        data = json.load(f)
    return [item["image_link"] for item in data]


def save_examples(examples: list[Example], outfile: str) -> None:
    """
    Save DSPy examples to a JSONL file.
    
    Args:
        examples: List of dspy.Example objects
        outfile: Output file path
    """
    with open(outfile, 'w') as f:
        for example in examples:
            # If using dicts
            # f.write(json.dumps(ex) + '\n')
            # If using DSPy Example objects
            f.write(json.dumps(example.toDict()) + '\n')


def load_examples(infile: str) -> list[dict]:
    """
    Load examples from a JSONL file.

    Args:
        infile: Input file path

    Returns:
        List of example dictionaries
    """
    loaded_examples = []
    with open(infile, 'r') as f:
        for line in f:
            loaded_examples.append(json.loads(line))
    return loaded_examples


def generate_examples_from_images(predictor, images_file: str = "examples/style_images_list.json",
                                output_file: str = "examples/style_examples.jsonl") -> list[Example]:
    """
    Generate style extraction examples from a list of images using the provided predictor.

    Args:
        predictor: DSPy predictor for style extraction
        images_file: Path to JSON file containing image data
        output_file: Path to save generated examples

    Returns:
        List of generated DSPy Example objects
    """
    examples = []

    for i, image in enumerate(get_images_links(images_file)):
        question = f"Describe the image: {image}"
        print(f"\n############## {i}  ##############\n", flush=True)

        try:
            response = predictor(instruction=question)
            print(f"{response.style}", flush=True)
            print(f"{response.metadata}", flush=True)
        except Exception as e:
            print(f"Error during DSPy prediction: {e}")
            import traceback
            traceback.print_exc()
        else:
            example = Example(
                instruction=question,
                style=response.style,
                metadata=response.metadata
            ).with_inputs('instruction')
            print(f"Example: {example.toDict()}", flush=True)
            examples.append(example)
            print(f"\n############## {i}  ##############\n")

    # Save examples to file
    save_examples(examples, output_file)
    return examples


async def generate_examples_from_images_async(predictor, images_file: str = "examples/style_images_list.json",
                                            output_file: str = "examples/style_examples.jsonl") -> list[Example]:
    """
    Generate style extraction examples from a list of images using the provided predictor asynchronously.

    Args:
        predictor: DSPy predictor for style extraction
        images_file: Path to JSON file containing image data
        output_file: Path to save generated examples

    Returns:
        List of generated DSPy Example objects
    """
    examples = []
    images = get_images_links(images_file)

    async def process_image(i, image):
        """Process a single image asynchronously."""
        question = f"Describe the image: {image}"
        print(f"\n############## {i}  ##############\n", flush=True)

        try:
            # Run the predictor in a thread pool to avoid blocking
            loop = asyncio.get_event_loop()
            response = await loop.run_in_executor(None, lambda: predictor(instruction=question))
            print(f"{response.style}", flush=True)
            print(f"{response.metadata}", flush=True)

            example = Example(
                instruction=question,
                style=response.style,
                metadata=response.metadata
            ).with_inputs('instruction')
            print(f"Example: {example.toDict()}", flush=True)
            print(f"\n############## {i}  ##############\n")
            return example

        except Exception as e:
            print(f"Error during DSPy prediction: {e}")
            import traceback
            traceback.print_exc()
            return None

    # Process images concurrently with a semaphore to limit concurrent requests
    semaphore = asyncio.Semaphore(5)  # Limit to 5 concurrent requests

    async def process_with_semaphore(i, image):
        async with semaphore:
            return await process_image(i, image)

    # Create tasks for all images
    tasks = [process_with_semaphore(i, image) for i, image in enumerate(images)]

    # Wait for all tasks to complete
    results = await asyncio.gather(*tasks, return_exceptions=True)

    # Filter out None results and exceptions
    for result in results:
        if isinstance(result, Example):
            examples.append(result)
        elif isinstance(result, Exception):
            print(f"Task failed with exception: {result}")

    # Save examples to file
    save_examples(examples, output_file)
    return examples
