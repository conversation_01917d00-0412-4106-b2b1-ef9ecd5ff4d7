"""
Test script for ground truth validation system.
"""

import sys
from pathlib import Path

# Add src to path
src_path = Path(__file__).parent / 'src'
if str(src_path) not in sys.path:
    sys.path.insert(0, str(src_path))

from validation.google_search_validator import GoogleSearchValidator

def test_ground_truth_validation():
    """Test the ground truth validation system."""
    print("🧪 Testing Ground Truth Validation System")
    print("=" * 50)
    
    # Initialize validator
    validator = GoogleSearchValidator(max_results=5)
    
    # Test topics
    test_cases = [
        {
            "topic": "web application performance optimization",
            "context": "frontend techniques and best practices"
        },
        {
            "topic": "machine learning model deployment",
            "context": "production environments and scalability"
        },
        {
            "topic": "database query optimization",
            "context": "SQL performance tuning strategies"
        }
    ]
    
    results = []
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n🔍 Test Case {i}: {test_case['topic']}")
        print("-" * 40)
        
        try:
            ideas, ground_truth, score = validator.run_validation(
                topic=test_case['topic'],
                context=test_case['context']
            )
            
            results.append({
                "test_case": i,
                "topic": test_case['topic'],
                "score": score.overall_score,
                "concept_overlap": score.concept_overlap,
                "theme_alignment": score.theme_alignment,
                "recommendation_quality": score.recommendation_quality,
                "novelty_score": score.novelty_score
            })
            
            print(f"✅ Test {i} completed - Score: {score.overall_score:.2f}")
            
            # Save individual results
            validator.save_validation_results(
                ideas, ground_truth, score,
                f"results/ground_truth_validation/test_case_{i}.json"
            )
            
        except Exception as e:
            print(f"❌ Test {i} failed: {e}")
            results.append({
                "test_case": i,
                "topic": test_case['topic'],
                "error": str(e)
            })
    
    # Summary
    print("\n" + "="*60)
    print("📊 GROUND TRUTH VALIDATION SUMMARY")
    print("="*60)
    
    successful_tests = [r for r in results if 'score' in r]
    
    if successful_tests:
        avg_score = sum(r['score'] for r in successful_tests) / len(successful_tests)
        avg_concept = sum(r['concept_overlap'] for r in successful_tests) / len(successful_tests)
        avg_theme = sum(r['theme_alignment'] for r in successful_tests) / len(successful_tests)
        avg_quality = sum(r['recommendation_quality'] for r in successful_tests) / len(successful_tests)
        avg_novelty = sum(r['novelty_score'] for r in successful_tests) / len(successful_tests)
        
        print(f"🎯 Average Overall Score: {avg_score:.2f}")
        print(f"📊 Average Concept Overlap: {avg_concept:.2f}")
        print(f"🎨 Average Theme Alignment: {avg_theme:.2f}")
        print(f"⭐ Average Recommendation Quality: {avg_quality:.2f}")
        print(f"💡 Average Novelty Score: {avg_novelty:.2f}")
        print()
        
        # Individual results
        for result in successful_tests:
            print(f"Test {result['test_case']}: {result['topic'][:40]}... - Score: {result['score']:.2f}")
    
    failed_tests = [r for r in results if 'error' in r]
    if failed_tests:
        print(f"\n❌ Failed Tests: {len(failed_tests)}")
        for result in failed_tests:
            print(f"Test {result['test_case']}: {result['error']}")
    
    print(f"\n✅ Completed {len(successful_tests)}/{len(test_cases)} tests successfully")
    
    return successful_tests, failed_tests

if __name__ == '__main__':
    test_ground_truth_validation()
