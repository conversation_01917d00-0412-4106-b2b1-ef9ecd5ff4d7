"""
Flask web application for Chain of Thought idea generator.
"""

from flask import Flask, render_template, request, jsonify
from flask_cors import CORS
import json
import traceback
import sys
from pathlib import Path

# Add the src directory to the path so we can import from it
src_path = Path(__file__).parent.parent
if str(src_path) not in sys.path:
    sys.path.insert(0, str(src_path))

from cot_system.cot_idea_generator import CoTIdeaSystem

# Configure Flask to use the correct template and static directories
template_dir = Path(__file__).parent / 'templates'
static_dir = Path(__file__).parent / 'static'

app = Flask(__name__,
           template_folder=str(template_dir),
           static_folder=str(static_dir))
CORS(app)

# Initialize the CoT system
cot_system = None

def initialize_system():
    """Initialize the CoT system."""
    global cot_system
    try:
        print("🔄 Initializing CoT system...")
        cot_system = CoTIdeaSystem()
        if cot_system.generator:
            print("✅ CoT system initialized successfully")
            return True
        else:
            print("❌ Failed to initialize CoT system - generator is None")
            return False
    except Exception as e:
        print(f"❌ Error initializing CoT system: {e}")
        import traceback
        traceback.print_exc()
        return False

@app.route('/')
def index():
    """Serve the main page."""
    return render_template('index.html')

@app.route('/api/templates')
def get_templates():
    """Get available templates."""
    if not cot_system:
        return jsonify({"error": "System not initialized"}), 500
    
    templates = []
    for template_type, description in cot_system.templates.items():
        templates.append({
            "id": template_type,
            "name": template_type.replace('_', ' ').title(),
            "description": description[:100] + "..." if len(description) > 100 else description
        })
    
    return jsonify({"templates": templates})

@app.route('/api/generate', methods=['POST'])
def generate_ideas():
    """Generate ideas based on user input."""
    if not cot_system:
        return jsonify({"error": "System not initialized"}), 500
    
    try:
        data = request.get_json()
        topic = data.get('topic', '').strip()
        template_id = data.get('template', '')
        custom_context = data.get('context', '').strip()
        
        if not topic:
            return jsonify({"error": "Topic is required"}), 400
        
        # Get context from template or use custom
        context = ""
        if template_id and template_id in cot_system.templates:
            context = cot_system.get_template_context(template_id)
        elif custom_context:
            context = custom_context
        
        # Generate ideas
        ideas = cot_system.generate_ideas(topic=topic, context=context)
        
        # Convert to dict for JSON response
        response_data = {
            "topic": ideas.topic,
            "thinking_chain": [
                {
                    "step_number": step.step_number,
                    "thought": step.thought,
                    "category": step.category,
                    "confidence": step.confidence
                }
                for step in ideas.thinking_chain
            ],
            "bullet_points": [
                {
                    "content": bp.content,
                    "priority": bp.priority,
                    "category": bp.category,
                    "supporting_evidence": bp.supporting_evidence,
                    "related_concepts": bp.related_concepts
                }
                for bp in ideas.bullet_points
            ],
            "summary": ideas.summary,
            "next_steps": ideas.next_steps
        }
        
        return jsonify(response_data)
        
    except Exception as e:
        print(f"Error generating ideas: {e}")
        traceback.print_exc()
        return jsonify({"error": f"Failed to generate ideas: {str(e)}"}), 500

@app.route('/api/save', methods=['POST'])
def save_ideas():
    """Save generated ideas to a file."""
    try:
        data = request.get_json()
        ideas_data = data.get('ideas')
        filename = data.get('filename', 'web_generated_ideas.json')
        
        if not ideas_data:
            return jsonify({"error": "No ideas data provided"}), 400
        
        # Save to file
        with open(filename, 'w') as f:
            json.dump(ideas_data, f, indent=2)
        
        return jsonify({"message": f"Ideas saved to {filename}", "filename": filename})
        
    except Exception as e:
        print(f"Error saving ideas: {e}")
        return jsonify({"error": f"Failed to save ideas: {str(e)}"}), 500

@app.route('/health')
def health_check():
    """Health check endpoint."""
    system_status = "initialized" if cot_system and cot_system.generator else "not initialized"
    return jsonify({
        "status": "healthy",
        "cot_system": system_status
    })

if __name__ == '__main__':
    print("🚀 Starting Chain of Thought Idea Generator Web App")
    print("=" * 50)

    # Initialize the system
    if initialize_system():
        print("🌐 Starting Flask server on http://localhost:5000")
        print("📝 Open your browser and navigate to the URL above")
        print("🛑 Press Ctrl+C to stop the server")
        print("-" * 50)
        try:
            app.run(debug=True, host='127.0.0.1', port=5000, use_reloader=False)
        except KeyboardInterrupt:
            print("\n👋 Server stopped by user")
        except Exception as e:
            print(f"❌ Server error: {e}")
    else:
        print("❌ Cannot start server - system initialization failed")
