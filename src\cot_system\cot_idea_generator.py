"""
Chain of Thought (CoT) system for bullet point and idea generation.
Uses structured thinking to generate comprehensive, well-organized ideas.
"""

import dspy
from typing import List, Dict, Any
from pydantic import BaseModel, Field
import json


class ThinkingStep(BaseModel):
    """A single step in the chain of thought process."""
    step_number: int = Field(description="The sequential number of this thinking step")
    thought: str = Field(description="The actual thought or reasoning at this step")
    category: str = Field(description="Category of thinking (analysis, synthesis, evaluation, etc.)")
    confidence: float = Field(description="Confidence level in this thought (0.0-1.0)")


class BulletPoint(BaseModel):
    """A structured bullet point with metadata."""
    content: str = Field(description="The main content of the bullet point")
    priority: str = Field(description="Priority level: high, medium, low")
    category: str = Field(description="Category or theme this bullet point belongs to")
    supporting_evidence: List[str] = Field(description="Supporting evidence or reasoning")
    related_concepts: List[str] = Field(description="Related concepts or ideas")


class IdeaStructure(BaseModel):
    """Complete idea structure with CoT reasoning."""
    topic: str = Field(description="The main topic or subject")
    thinking_chain: List[ThinkingStep] = Field(description="Chain of thought steps")
    bullet_points: List[BulletPoint] = Field(description="Generated bullet points")
    summary: str = Field(description="Overall summary of the idea")
    next_steps: List[str] = Field(description="Suggested next steps or actions")


class CoTIdeaSignature(dspy.Signature):
    """Generate ideas using chain of thought reasoning.

    Think step by step:
    1. First, analyze the topic and context
    2. Break down the problem into components
    3. Generate multiple perspectives and approaches
    4. Synthesize ideas into organized bullet points
    5. Evaluate and prioritize the ideas
    6. Suggest concrete next steps

    Output should be a JSON structure matching the IdeaStructure Pydantic model:
    - topic: The main topic being analyzed
    - thinking_chain: Array of ThinkingStep objects with {step_number, thought, category, confidence}
    - bullet_points: Array of BulletPoint objects with {content, priority, category, supporting_evidence, related_concepts}
    - summary: Overall summary
    - next_steps: Array of actionable steps
    """

    topic: str = dspy.InputField(desc="The topic or subject to generate ideas about")
    context: str = dspy.InputField(desc="Additional context, constraints, or specific requirements")

    thinking_process: str = dspy.OutputField(desc="Detailed step-by-step chain of thought reasoning showing your analysis, synthesis, and evaluation process")
    structured_ideas: str = dspy.OutputField(desc="""JSON structure matching the IdeaStructure Pydantic model. Format:
    {
      "topic": "string - the main topic being analyzed",
      "thinking_chain": [
        {
          "step_number": 1,
          "thought": "detailed reasoning step",
          "category": "analysis|synthesis|evaluation|planning|creative|critical",
          "confidence": 0.8
        }
      ],
      "bullet_points": [
        {
          "content": "actionable idea or insight",
          "priority": "high|medium|low",
          "category": "implementation|research|strategy|process|creative|technical",
          "supporting_evidence": ["evidence point 1", "evidence point 2"],
          "related_concepts": ["concept 1", "concept 2"]
        }
      ],
      "summary": "concise overview of the key insights and recommendations",
      "next_steps": ["specific actionable step 1", "specific actionable step 2"]
    }
    Ensure all arrays are properly formatted and step_number starts from 1.""")


class CoTIdeaGenerator(dspy.Module):
    """Chain of Thought module for generating structured ideas and bullet points."""
    
    def __init__(self):
        super().__init__()
        self.generate_ideas = dspy.ChainOfThought(CoTIdeaSignature)
    
    def forward(self, topic: str, context: str = "") -> IdeaStructure:
        """Generate structured ideas using chain of thought reasoning."""
        
        # Generate ideas with CoT
        result = self.generate_ideas(topic=topic, context=context)
        
        try:
            # Parse the structured output
            ideas_data = json.loads(result.structured_ideas)
            
            # Create thinking chain
            thinking_chain = []
            if "thinking_chain" in ideas_data:
                for step in ideas_data["thinking_chain"]:
                    thinking_step = ThinkingStep(
                        step_number=step.get("step_number", len(thinking_chain) + 1),
                        thought=step.get("thought", ""),
                        category=step.get("category", "general"),
                        confidence=step.get("confidence", 0.5)
                    )
                    thinking_chain.append(thinking_step)
            elif "thinking_steps" in ideas_data:  # Backward compatibility
                for i, step in enumerate(ideas_data["thinking_steps"], 1):
                    thinking_step = ThinkingStep(
                        step_number=i,
                        thought=step.get("thought", ""),
                        category=step.get("category", "general"),
                        confidence=step.get("confidence", 0.8)
                    )
                    thinking_chain.append(thinking_step)
            
            # Create bullet points
            bullet_points = []
            if "bullet_points" in ideas_data:
                for bp_data in ideas_data["bullet_points"]:
                    # Ensure supporting_evidence and related_concepts are lists
                    supporting_evidence = bp_data.get("supporting_evidence", [])
                    if isinstance(supporting_evidence, str):
                        supporting_evidence = [supporting_evidence] if supporting_evidence else []

                    related_concepts = bp_data.get("related_concepts", [])
                    if isinstance(related_concepts, str):
                        related_concepts = [related_concepts] if related_concepts else []

                    bullet_point = BulletPoint(
                        content=bp_data.get("content", ""),
                        priority=bp_data.get("priority", "medium"),
                        category=bp_data.get("category", "general"),
                        supporting_evidence=supporting_evidence,
                        related_concepts=related_concepts
                    )
                    bullet_points.append(bullet_point)
            
            # Create complete structure
            idea_structure = IdeaStructure(
                topic=ideas_data.get("topic", topic),  # Use topic from JSON or fallback to input
                thinking_chain=thinking_chain,
                bullet_points=bullet_points,
                summary=ideas_data.get("summary", ""),
                next_steps=ideas_data.get("next_steps", [])
            )
            
            return idea_structure
            
        except (json.JSONDecodeError, KeyError) as e:
            # Fallback: create basic structure from raw output
            print(f"Warning: Could not parse structured output, using fallback: {e}")
            
            # Extract bullet points from thinking process
            lines = result.thinking_process.split('\n')
            bullet_points = []
            
            for line in lines:
                line = line.strip()
                if line.startswith('•') or line.startswith('-') or line.startswith('*'):
                    content = line[1:].strip()
                    if content:
                        bullet_point = BulletPoint(
                            content=content,
                            priority="medium",
                            category="general",
                            supporting_evidence=[],
                            related_concepts=[]
                        )
                        bullet_points.append(bullet_point)
            
            # Create basic thinking chain
            thinking_chain = [
                ThinkingStep(
                    step_number=1,
                    thought=result.thinking_process,
                    category="general",
                    confidence=0.7
                )
            ]
            
            idea_structure = IdeaStructure(
                topic=topic,
                thinking_chain=thinking_chain,
                bullet_points=bullet_points,
                summary=f"Ideas generated for: {topic}",
                next_steps=["Review and refine generated ideas", "Prioritize implementation"]
            )
            
            return idea_structure


class CoTIdeaSystem:
    """Complete system for Chain of Thought idea generation."""

    def __init__(self):
        self.generator = None
        self.templates = self._load_templates()
        self.setup_dspy()
    
    def _load_templates(self) -> Dict[str, str]:
        """Load context templates for different types of idea generation."""
        return {
            "business": "Focus on market opportunities, revenue models, competitive advantages, and scalability. Consider customer needs, market size, and implementation feasibility.",
            "technical": "Consider technical feasibility, architecture, scalability, security, and maintainability. Think about tools, frameworks, and best practices.",
            "creative": "Explore innovative approaches, artistic elements, user experience, and emotional impact. Consider aesthetics, storytelling, and engagement.",
            "research": "Focus on methodology, data sources, analysis approaches, and validation methods. Consider literature review, hypotheses, and experimental design.",
            "problem_solving": "Break down the problem systematically, identify root causes, generate multiple solution approaches, and evaluate trade-offs.",
            "strategic": "Consider long-term vision, stakeholder impact, resource allocation, risk assessment, and competitive positioning.",
            "educational": "Focus on learning objectives, pedagogical approaches, assessment methods, and student engagement strategies.",
            "process_improvement": "Analyze current state, identify bottlenecks, design optimized workflows, and measure success metrics."
        }

    def setup_dspy(self):
        """Setup DSPy with appropriate language model."""
        try:
            lm = dspy.LM("openai/gpt-4o-mini", cache_in_memory=False)
            dspy.configure(lm=lm)
            self.generator = CoTIdeaGenerator()
            print("✅ DSPy setup complete for CoT idea generation")
        except Exception as e:
            print(f"❌ Error setting up DSPy: {e}")

    def get_template_context(self, template_type: str) -> str:
        """Get context template for specific type of idea generation."""
        return self.templates.get(template_type, "")
    
    def generate_ideas(self, topic: str, context: str = "") -> IdeaStructure:
        """Generate ideas for a given topic with context."""
        if not self.generator:
            raise ValueError("DSPy not properly initialized")
        
        print(f"🧠 Generating ideas for: {topic}")
        if context:
            print(f"📝 Context: {context}")
        
        result = self.generator(topic=topic, context=context)
        return result
    
    def display_ideas(self, ideas: IdeaStructure):
        """Display the generated ideas in a formatted way."""
        print(f"\n🎯 Topic: {ideas.topic}")
        print("=" * 60)
        
        # Display thinking process
        if ideas.thinking_chain:
            print(f"\n🧠 Chain of Thought Process:")
            for step in ideas.thinking_chain:
                print(f"   {step.step_number}. [{step.category.upper()}] {step.thought}")
                print(f"      Confidence: {step.confidence:.1f}")
        
        # Display bullet points by category
        if ideas.bullet_points:
            print(f"\n📋 Generated Ideas:")
            
            # Group by category
            categories = {}
            for bp in ideas.bullet_points:
                if bp.category not in categories:
                    categories[bp.category] = []
                categories[bp.category].append(bp)
            
            for category, points in categories.items():
                print(f"\n   📂 {category.upper()}:")
                
                # Sort by priority
                priority_order = {"high": 0, "medium": 1, "low": 2}
                points.sort(key=lambda x: priority_order.get(x.priority, 1))
                
                for bp in points:
                    priority_icon = {"high": "🔥", "medium": "⭐", "low": "💡"}
                    icon = priority_icon.get(bp.priority, "•")
                    
                    print(f"      {icon} {bp.content}")
                    
                    if bp.supporting_evidence:
                        print(f"         Evidence: {', '.join(bp.supporting_evidence)}")
                    
                    if bp.related_concepts:
                        print(f"         Related: {', '.join(bp.related_concepts)}")
        
        # Display summary
        if ideas.summary:
            print(f"\n📊 Summary:")
            print(f"   {ideas.summary}")
        
        # Display next steps
        if ideas.next_steps:
            print(f"\n🚀 Next Steps:")
            for i, step in enumerate(ideas.next_steps, 1):
                print(f"   {i}. {step}")
    
    def save_ideas(self, ideas: IdeaStructure, filename: str):
        """Save ideas to a JSON file."""
        try:
            with open(filename, 'w') as f:
                json.dump(ideas.model_dump(), f, indent=2)
            print(f"💾 Ideas saved to: {filename}")
        except Exception as e:
            print(f"❌ Error saving ideas: {e}")
    
    def interactive_session(self):
        """Run an interactive idea generation session."""
        print("🧠 Chain of Thought Idea Generator")
        print("=" * 50)
        
        while True:
            print("\nOptions:")
            print("1. Generate new ideas")
            print("2. Exit")
            
            choice = input("Enter choice (1-2): ").strip()
            
            if choice == "1":
                topic = input("Enter topic: ").strip()
                if not topic:
                    print("❌ Please enter a topic")
                    continue

                # Show template options
                print("\nAvailable templates:")
                for i, template_type in enumerate(self.templates.keys(), 1):
                    print(f"   {i}. {template_type.title()}")
                print(f"   {len(self.templates) + 1}. Custom context")

                template_choice = input(f"Choose template (1-{len(self.templates) + 1}) or press Enter for none: ").strip()

                context = ""
                if template_choice.isdigit():
                    choice_num = int(template_choice)
                    if 1 <= choice_num <= len(self.templates):
                        template_type = list(self.templates.keys())[choice_num - 1]
                        context = self.get_template_context(template_type)
                        print(f"✅ Using {template_type} template")
                    elif choice_num == len(self.templates) + 1:
                        context = input("Enter custom context: ").strip()

                if not context:
                    context = input("Enter additional context (optional): ").strip()
                
                try:
                    ideas = self.generate_ideas(topic, context)
                    self.display_ideas(ideas)
                    
                    # Ask if user wants to save
                    save_choice = input("\nSave ideas to file? (y/n): ").strip().lower()
                    if save_choice == 'y':
                        filename = f"ideas_{topic.replace(' ', '_').lower()}.json"
                        self.save_ideas(ideas, filename)
                
                except Exception as e:
                    print(f"❌ Error generating ideas: {e}")
            
            elif choice == "2":
                print("👋 Goodbye!")
                break
            
            else:
                print("❌ Invalid choice")


def main():
    """Main function to run the CoT idea generation system."""
    system = CoTIdeaSystem()
    
    if system.generator:
        system.interactive_session()
    else:
        print("❌ Failed to initialize the system")


if __name__ == "__main__":
    main()
