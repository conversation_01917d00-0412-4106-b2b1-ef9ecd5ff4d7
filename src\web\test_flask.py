"""
Minimal Flask test to verify Flask is working.
"""

from flask import Flask

app = Flask(__name__)

@app.route('/')
def hello():
    return '<h1>Hello! Flask is working!</h1><p>This is a test page.</p>'

@app.route('/test')
def test():
    return {'status': 'ok', 'message': 'Flask API is working'}

if __name__ == '__main__':
    print("🧪 Starting minimal Flask test server...")
    print("🌐 Server will be available at: http://localhost:5001")
    app.run(debug=True, host='127.0.0.1', port=5001)
