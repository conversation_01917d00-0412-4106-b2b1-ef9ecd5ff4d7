"""
Final integration test to verify the complete Chain of Thought web application.
This test will verify all components work together properly.
"""

import os
import sys
import time
import json
import subprocess
import threading
import requests
from pathlib import Path

class CoTWebAppTester:
    def __init__(self):
        self.base_url = "http://localhost:5000"
        self.server_process = None
        self.test_results = []
    
    def log_test(self, test_name, success, message=""):
        """Log test results."""
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} {test_name}")
        if message:
            print(f"    {message}")
        
        self.test_results.append({
            "test": test_name,
            "success": success,
            "message": message
        })
    
    def check_files(self):
        """Check if all required files exist."""
        print("\n📁 Checking File Structure...")
        
        required_files = [
            'cot_idea_generator.py',
            'app.py',
            'templates/index.html',
            'static/css/style.css',
            'static/js/app.js'
        ]
        
        all_exist = True
        for file_path in required_files:
            exists = Path(file_path).exists()
            self.log_test(f"File exists: {file_path}", exists)
            if not exists:
                all_exist = False
        
        return all_exist
    
    def check_dependencies(self):
        """Check if all dependencies are installed."""
        print("\n📦 Checking Dependencies...")
        
        dependencies = [
            ('flask', 'Flask'),
            ('flask_cors', 'Flask-CORS'),
            ('dspy', 'DSPy'),
            ('openai', 'OpenAI'),
            ('pydantic', 'Pydantic'),
            ('requests', 'Requests')
        ]
        
        all_installed = True
        for module, name in dependencies:
            try:
                __import__(module)
                self.log_test(f"Import {name}", True)
            except ImportError:
                self.log_test(f"Import {name}", False, f"Module {module} not found")
                all_installed = False
        
        return all_installed
    
    def test_cot_system(self):
        """Test the CoT system initialization."""
        print("\n🧠 Testing CoT System...")
        
        try:
            from cot_idea_generator import CoTIdeaSystem
            system = CoTIdeaSystem()
            
            if system.generator:
                self.log_test("CoT System Initialization", True, "System initialized successfully")
                
                # Test idea generation
                try:
                    result = system.generate_ideas(
                        topic="test topic",
                        context="This is a test"
                    )
                    
                    has_thinking = 'thinking_chain' in result and len(result['thinking_chain']) > 0
                    has_ideas = 'bullet_points' in result and len(result['bullet_points']) > 0
                    
                    self.log_test("CoT Idea Generation", has_thinking and has_ideas, 
                                f"Generated {len(result.get('thinking_chain', []))} thinking steps and {len(result.get('bullet_points', []))} ideas")
                    
                    return True
                    
                except Exception as e:
                    self.log_test("CoT Idea Generation", False, f"Generation failed: {e}")
                    return False
            else:
                self.log_test("CoT System Initialization", False, "Generator is None")
                return False
                
        except Exception as e:
            self.log_test("CoT System Initialization", False, f"Import/init failed: {e}")
            return False
    
    def start_server(self):
        """Start the Flask server in background."""
        print("\n🚀 Starting Flask Server...")
        
        try:
            # Start server process
            self.server_process = subprocess.Popen(
                [sys.executable, 'app.py'],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            
            # Wait for server to start
            time.sleep(5)
            
            # Check if server is running
            if self.server_process.poll() is None:
                self.log_test("Flask Server Start", True, "Server started successfully")
                return True
            else:
                stdout, stderr = self.server_process.communicate()
                self.log_test("Flask Server Start", False, f"Server failed to start: {stderr}")
                return False
                
        except Exception as e:
            self.log_test("Flask Server Start", False, f"Failed to start server: {e}")
            return False
    
    def test_api_endpoints(self):
        """Test all API endpoints."""
        print("\n🌐 Testing API Endpoints...")
        
        # Wait a bit more for server to be ready
        time.sleep(2)
        
        # Test health endpoint
        try:
            response = requests.get(f"{self.base_url}/health", timeout=10)
            self.log_test("Health Endpoint", response.status_code == 200, 
                         f"Status: {response.status_code}")
        except Exception as e:
            self.log_test("Health Endpoint", False, f"Request failed: {e}")
            return False
        
        # Test templates endpoint
        try:
            response = requests.get(f"{self.base_url}/api/templates", timeout=10)
            success = response.status_code == 200
            if success:
                data = response.json()
                template_count = len(data.get('templates', []))
                self.log_test("Templates Endpoint", True, f"Retrieved {template_count} templates")
            else:
                self.log_test("Templates Endpoint", False, f"Status: {response.status_code}")
        except Exception as e:
            self.log_test("Templates Endpoint", False, f"Request failed: {e}")
        
        # Test generate endpoint
        try:
            test_data = {
                "topic": "test web application",
                "template": "technical",
                "context": "Focus on testing"
            }
            
            response = requests.post(f"{self.base_url}/api/generate", 
                                   json=test_data, timeout=30)
            
            success = response.status_code == 200
            if success:
                data = response.json()
                thinking_steps = len(data.get('thinking_chain', []))
                ideas_count = len(data.get('bullet_points', []))
                self.log_test("Generate Endpoint", True, 
                             f"Generated {thinking_steps} thinking steps and {ideas_count} ideas")
            else:
                self.log_test("Generate Endpoint", False, f"Status: {response.status_code}")
                
        except Exception as e:
            self.log_test("Generate Endpoint", False, f"Request failed: {e}")
        
        return True
    
    def cleanup(self):
        """Clean up resources."""
        if self.server_process:
            self.server_process.terminate()
            self.server_process.wait()
            print("\n🛑 Server stopped")
    
    def run_full_test(self):
        """Run the complete test suite."""
        print("🧪 Chain of Thought Web Application - Integration Test")
        print("=" * 60)
        
        try:
            # Step 1: Check files
            if not self.check_files():
                print("\n❌ File structure check failed. Cannot continue.")
                return False
            
            # Step 2: Check dependencies
            if not self.check_dependencies():
                print("\n❌ Dependency check failed. Cannot continue.")
                return False
            
            # Step 3: Test CoT system
            if not self.test_cot_system():
                print("\n❌ CoT system test failed. Cannot continue.")
                return False
            
            # Step 4: Start server
            if not self.start_server():
                print("\n❌ Server start failed. Cannot continue.")
                return False
            
            # Step 5: Test API endpoints
            self.test_api_endpoints()
            
            # Summary
            self.print_summary()
            
            return True
            
        except KeyboardInterrupt:
            print("\n⏹️ Test interrupted by user")
            return False
        finally:
            self.cleanup()
    
    def print_summary(self):
        """Print test summary."""
        print("\n" + "=" * 60)
        print("📊 TEST SUMMARY")
        print("=" * 60)
        
        passed = sum(1 for result in self.test_results if result['success'])
        total = len(self.test_results)
        
        print(f"✅ Passed: {passed}/{total} tests")
        
        if passed == total:
            print("\n🎉 ALL TESTS PASSED!")
            print("🌐 Your Chain of Thought web application is ready!")
            print("🔗 Open http://localhost:5000 in your browser")
        else:
            print(f"\n⚠️  {total - passed} tests failed")
            print("❌ Failed tests:")
            for result in self.test_results:
                if not result['success']:
                    print(f"   • {result['test']}: {result['message']}")

def main():
    """Main test function."""
    tester = CoTWebAppTester()
    success = tester.run_full_test()
    
    if success:
        print("\n🚀 Ready to use the Chain of Thought Idea Generator!")
        print("📝 Next steps:")
        print("   1. Open http://localhost:5000 in your browser")
        print("   2. Enter a topic to generate ideas")
        print("   3. Choose a template for better results")
        print("   4. Enjoy structured AI reasoning!")
    else:
        print("\n❌ Integration test failed. Please check the errors above.")
        sys.exit(1)

if __name__ == '__main__':
    main()
