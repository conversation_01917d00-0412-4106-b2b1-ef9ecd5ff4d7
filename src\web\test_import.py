"""
Test script to debug import issues in the web directory.
"""

import sys
from pathlib import Path

print("🔍 Testing imports from web directory")
print("=" * 40)

# Add parent directory to path
parent_dir = Path(__file__).parent.parent
print(f"📁 Parent directory: {parent_dir}")
if str(parent_dir) not in sys.path:
    sys.path.insert(0, str(parent_dir))
    print("✅ Added parent directory to sys.path")

print(f"🐍 Python path: {sys.path[:3]}...")

try:
    print("📦 Testing CoT system import...")
    from cot_system.cot_idea_generator import CoTIdeaSystem
    print("✅ CoT system import successful")
    
    print("🔄 Testing CoT system initialization...")
    system = CoTIdeaSystem()
    print("✅ CoT system initialization successful")
    
    print("📦 Testing Flask imports...")
    from flask import Flask
    print("✅ Flask import successful")
    
    print("🎉 All imports working correctly!")
    
except Exception as e:
    print(f"❌ Import error: {e}")
    import traceback
    traceback.print_exc()
