"""
Style-aware verification system that understands the tile structure.
Each style has 4 tiles (2x2 grid) from the same original 1024x1024 image.
"""

import asyncio
import json
import os
from datetime import datetime
from pathlib import Path
import dspy
from dspy import Example
from collections import defaultdict
import re

# Import our modules
from main import setup_dspy, create_predictor, optimize_predictor_labeled_fewshot
from utils.data_utils import load_examples
from simple_feedback_test import generate_image_from_style, analyze_generated_image, calculate_style_similarity


class StyleAwareVerificationSystem:
    """Verification system that understands the 4-tile structure per style."""
    
    def __init__(self):
        self.original_predictor = None
        self.optimized_predictor = None
        self.style_groups = {}
        self.cost_per_image = 0.04
    
    def setup(self):
        """Setup DSPy and create original predictor."""
        setup_dspy()
        lm = dspy.LM("openai/gpt-4o-mini", cache_in_memory=False)
        dspy.configure(lm=lm)
        self.original_predictor = create_predictor()
        print("✅ DSPy setup complete")
    
    def group_examples_by_style(self, examples: list) -> dict:
        """Group examples by their base style name (ignoring tile position)."""
        style_groups = defaultdict(list)
        
        for example in examples:
            try:
                # Extract the image path from instruction
                instruction = example["instruction"]
                image_path = instruction.replace("Describe the image: ", "")
                
                # Extract base style name from filename
                # Pattern: style_name-1024x1024_tile_row_col.png
                filename = Path(image_path).name
                
                # Remove the tile suffix to get base style name
                # e.g., "16-bit_style_bear_head-1024x1024_tile_0_0.png" -> "16-bit_style_bear_head"
                base_name = re.sub(r'-1024x1024_tile_\d_\d\.png$', '', filename)
                
                # Get metadata for style name
                metadata = json.loads(example["metadata"])
                style_name = metadata.get("style_name", base_name)
                
                # Add tile position info
                tile_match = re.search(r'_tile_(\d)_(\d)\.png$', filename)
                if tile_match:
                    row, col = int(tile_match.group(1)), int(tile_match.group(2))
                    tile_position = f"{row}_{col}"
                else:
                    tile_position = "unknown"
                
                example_with_tile_info = {
                    **example,
                    "base_style_name": base_name,
                    "tile_position": tile_position,
                    "image_path": image_path,
                    "style_name": style_name
                }
                
                style_groups[base_name].append(example_with_tile_info)
                
            except Exception as e:
                print(f"Warning: Could not process example: {e}")
                continue
        
        return dict(style_groups)
    
    def select_diverse_styles(self, style_groups: dict, num_styles: int) -> dict:
        """Select diverse styles, ensuring we have complete tile sets."""
        selected_styles = {}
        
        # Filter to styles that have all 4 tiles
        complete_styles = {
            style_name: tiles 
            for style_name, tiles in style_groups.items() 
            if len(tiles) == 4
        }
        
        print(f"📊 Found {len(complete_styles)} complete styles (with all 4 tiles)")
        print(f"📊 Found {len(style_groups)} total styles")
        
        # Select the first num_styles complete styles
        selected_style_names = list(complete_styles.keys())[:num_styles]
        
        for style_name in selected_style_names:
            selected_styles[style_name] = complete_styles[style_name]
        
        return selected_styles
    
    async def test_style_consistency(self, style_name: str, tiles: list) -> dict:
        """Test consistency across all 4 tiles of the same style."""
        print(f"\n🎨 Testing style consistency for: {style_name}")
        
        # Sort tiles by position for consistent ordering
        tiles_sorted = sorted(tiles, key=lambda x: x["tile_position"])
        
        consistency_results = []
        generated_images = []
        
        # Generate images from each tile's style analysis
        for i, tile in enumerate(tiles_sorted):
            try:
                print(f"   📍 Processing tile {tile['tile_position']} ({i+1}/4)")
                
                # Generate image from this tile's style analysis
                timestamp = datetime.now().strftime("%H%M%S")
                generated_path = f"style_verification/{style_name}_tile_{tile['tile_position']}_{timestamp}.png"
                os.makedirs("style_verification", exist_ok=True)
                
                await generate_image_from_style(tile["style"], generated_path)
                
                # Analyze generated image
                analysis = await analyze_generated_image(self.original_predictor, generated_path)
                
                # Calculate similarity to expected style
                similarity = calculate_style_similarity(tile["style"], analysis["style"])
                
                tile_result = {
                    "tile_position": tile["tile_position"],
                    "original_tile_path": tile["image_path"],
                    "generated_image_path": generated_path,
                    "expected_style": tile["style"],
                    "generated_analysis": analysis["style"],
                    "similarity_score": similarity["overall_score"]
                }
                
                consistency_results.append(tile_result)
                generated_images.append(generated_path)
                
                print(f"      📊 Similarity: {similarity['overall_score']:.3f}")
                
            except Exception as e:
                print(f"      ❌ Error processing tile {tile['tile_position']}: {e}")
                continue
        
        if not consistency_results:
            return {"error": "No tiles processed successfully"}
        
        # Calculate consistency metrics
        similarities = [r["similarity_score"] for r in consistency_results]
        
        consistency_metrics = {
            "style_name": style_name,
            "num_tiles_processed": len(consistency_results),
            "tile_results": consistency_results,
            "generated_images": generated_images,
            "consistency_stats": {
                "mean_similarity": sum(similarities) / len(similarities),
                "std_similarity": (sum((s - sum(similarities)/len(similarities))**2 for s in similarities) / len(similarities))**0.5,
                "min_similarity": min(similarities),
                "max_similarity": max(similarities),
                "similarity_range": max(similarities) - min(similarities)
            }
        }
        
        stats = consistency_metrics["consistency_stats"]
        print(f"   📈 Style consistency results:")
        print(f"      Mean similarity: {stats['mean_similarity']:.3f}")
        print(f"      Std deviation: {stats['std_similarity']:.3f}")
        print(f"      Range: {stats['min_similarity']:.3f} - {stats['max_similarity']:.3f}")
        
        # Determine consistency level
        if stats['std_similarity'] < 0.1:
            consistency_level = "High"
        elif stats['std_similarity'] < 0.2:
            consistency_level = "Medium"
        else:
            consistency_level = "Low"
        
        consistency_metrics["consistency_level"] = consistency_level
        print(f"      🎯 Consistency level: {consistency_level}")
        
        return consistency_metrics
    
    async def run_style_aware_optimization(self, num_styles: int = 3):
        """Run style-aware optimization using multiple tiles per style."""
        print("🎨 Style-Aware Optimization System")
        print("=" * 60)
        print(f"💰 Estimated cost: ${num_styles * 4 * self.cost_per_image:.2f} for {num_styles} styles (4 tiles each)")
        print("🔍 This uses all 4 tiles per style for better optimization")

        # Setup
        self.setup()

        # Load and group examples
        print("\n1️⃣ Loading and grouping examples by style...")
        examples_file = "examples/groovjones_styles/tile_examples.jsonl"
        all_examples = load_examples(examples_file)

        style_groups = self.group_examples_by_style(all_examples)
        selected_styles = self.select_diverse_styles(style_groups, num_styles)

        if not selected_styles:
            print("❌ No complete styles found")
            return

        print(f"✅ Selected {len(selected_styles)} styles with {sum(len(tiles) for tiles in selected_styles.values())} total tiles")

        # Collect feedback data from all tiles
        print(f"\n2️⃣ Collecting feedback data from all tiles...")
        feedback_data = []

        for style_name, tiles in selected_styles.items():
            print(f"\n🎨 Processing style: {style_name}")

            for i, tile in enumerate(tiles, 1):
                try:
                    print(f"   📍 Tile {tile['tile_position']} ({i}/4)")

                    # Generate image
                    timestamp = datetime.now().strftime("%H%M%S")
                    generated_path = f"style_optimization/{style_name}_tile_{tile['tile_position']}_{timestamp}.png"
                    os.makedirs("style_optimization", exist_ok=True)

                    await generate_image_from_style(tile["style"], generated_path)

                    # Analyze with original predictor
                    analysis = await analyze_generated_image(self.original_predictor, generated_path)

                    # Calculate similarity
                    similarity = calculate_style_similarity(tile["style"], analysis["style"])

                    feedback_item = {
                        "style_name": style_name,
                        "tile_position": tile["tile_position"],
                        "instruction": tile["instruction"],
                        "expected_style": tile["style"],
                        "actual_style": analysis["style"],
                        "similarity_score": similarity["overall_score"],
                        "generated_image_path": generated_path,
                        "original_tile_path": tile["image_path"]
                    }

                    feedback_data.append(feedback_item)
                    print(f"      📊 Similarity: {similarity['overall_score']:.3f}")

                except Exception as e:
                    print(f"      ❌ Error: {e}")
                    continue

        if not feedback_data:
            print("❌ No feedback data collected")
            return

        avg_before = sum(item["similarity_score"] for item in feedback_data) / len(feedback_data)
        print(f"\n📊 Average similarity before optimization: {avg_before:.3f}")

        # Create optimization examples
        print(f"\n3️⃣ Creating optimization examples...")
        optimization_examples = []

        for feedback in feedback_data:
            example = Example(
                instruction=feedback["instruction"],
                style=feedback["expected_style"]
            ).with_inputs('instruction')
            optimization_examples.append(example)

        print(f"✅ Created {len(optimization_examples)} optimization examples")

        # Optimize predictor
        print(f"\n4️⃣ Optimizing predictor with LabeledFewShot...")
        try:
            k = min(len(optimization_examples), 16)
            self.optimized_predictor = optimize_predictor_labeled_fewshot(
                self.original_predictor,
                optimization_examples,
                k=k
            )
            print("✅ Optimization complete")
        except Exception as e:
            print(f"❌ Optimization error: {e}")
            self.optimized_predictor = self.original_predictor

        # Test optimized predictor
        print(f"\n5️⃣ Testing optimized predictor...")
        test_results = []

        for feedback in feedback_data:
            try:
                # Analyze with optimized predictor
                optimized_analysis = await analyze_generated_image(
                    self.optimized_predictor,
                    feedback["generated_image_path"]
                )

                # Calculate new similarity
                new_similarity = calculate_style_similarity(
                    feedback["expected_style"],
                    optimized_analysis["style"]
                )

                improvement = new_similarity["overall_score"] - feedback["similarity_score"]

                test_result = {
                    "style_name": feedback["style_name"],
                    "tile_position": feedback["tile_position"],
                    "original_similarity": feedback["similarity_score"],
                    "optimized_similarity": new_similarity["overall_score"],
                    "improvement": improvement
                }

                test_results.append(test_result)

            except Exception as e:
                print(f"❌ Error testing: {e}")
                continue

        # Analyze results by style
        print(f"\n6️⃣ Style-wise optimization results:")

        style_results = defaultdict(list)
        for result in test_results:
            style_results[result["style_name"]].append(result)

        overall_improvements = []

        for style_name, results in style_results.items():
            improvements = [r["improvement"] for r in results]
            avg_improvement = sum(improvements) / len(improvements)
            overall_improvements.extend(improvements)

            print(f"\n🎨 {style_name}:")
            print(f"   Tiles tested: {len(results)}")
            print(f"   Average improvement: {avg_improvement:+.3f}")
            print(f"   Range: {min(improvements):+.3f} to {max(improvements):+.3f}")

        # Overall statistics
        avg_improvement = sum(overall_improvements) / len(overall_improvements)
        improved_count = len([i for i in overall_improvements if i > 0])

        print(f"\n📊 Overall Optimization Results:")
        print(f"   Total tiles: {len(test_results)}")
        print(f"   Average improvement: {avg_improvement:+.3f}")
        print(f"   Improved tiles: {improved_count}/{len(test_results)} ({improved_count/len(test_results)*100:.1f}%)")

        # Save results
        results_file = f"style_optimization/style_aware_optimization_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"

        results = {
            "configuration": {
                "num_styles": len(selected_styles),
                "total_tiles": len(feedback_data),
                "optimization_method": "LabeledFewShot",
                "k_value": k
            },
            "feedback_data": feedback_data,
            "test_results": test_results,
            "style_summaries": {
                style_name: {
                    "num_tiles": len(results),
                    "average_improvement": sum(r["improvement"] for r in results) / len(results),
                    "improvements": [r["improvement"] for r in results]
                }
                for style_name, results in style_results.items()
            },
            "overall_statistics": {
                "average_improvement": avg_improvement,
                "improved_count": improved_count,
                "total_count": len(test_results),
                "improvement_rate": improved_count / len(test_results)
            },
            "timestamp": datetime.now().isoformat()
        }

        with open(results_file, 'w') as f:
            json.dump(results, f, indent=2)

        print(f"\n📁 Results saved to: {results_file}")
        print("✅ Style-aware optimization completed!")

        return results

    async def run_style_aware_verification(self, num_styles: int = 3):
        """Run comprehensive style-aware verification."""
        print("🎨 Style-Aware Verification System")
        print("=" * 60)
        print(f"💰 Estimated cost: ${num_styles * 4 * self.cost_per_image:.2f} for {num_styles} styles (4 tiles each)")
        print("🔍 This test validates consistency across all 4 tiles per style")
        
        # Setup
        self.setup()
        
        # Load and group examples
        print("\n1️⃣ Loading and grouping examples by style...")
        examples_file = "examples/groovjones_styles/tile_examples.jsonl"
        all_examples = load_examples(examples_file)
        
        style_groups = self.group_examples_by_style(all_examples)
        print(f"✅ Found {len(style_groups)} unique styles")
        
        # Select diverse complete styles
        selected_styles = self.select_diverse_styles(style_groups, num_styles)
        print(f"✅ Selected {len(selected_styles)} complete styles for testing")
        
        if not selected_styles:
            print("❌ No complete styles found for testing")
            return
        
        # Test each style
        print(f"\n2️⃣ Testing style consistency across tiles...")
        all_results = []
        
        for style_name, tiles in selected_styles.items():
            try:
                style_results = await self.test_style_consistency(style_name, tiles)
                if "error" not in style_results:
                    all_results.append(style_results)
            except Exception as e:
                print(f"❌ Error testing style {style_name}: {e}")
                continue
        
        if not all_results:
            print("❌ No style results generated")
            return
        
        # Comprehensive analysis
        print(f"\n3️⃣ Comprehensive Style Analysis:")
        
        # Overall statistics
        all_similarities = []
        consistency_levels = {"High": 0, "Medium": 0, "Low": 0}
        
        for result in all_results:
            stats = result["consistency_stats"]
            all_similarities.extend([r["similarity_score"] for r in result["tile_results"]])
            consistency_levels[result["consistency_level"]] += 1
        
        print(f"\n📊 Overall Performance:")
        print(f"   Total tiles tested: {len(all_similarities)}")
        print(f"   Average similarity: {sum(all_similarities)/len(all_similarities):.3f}")
        print(f"   Overall range: {min(all_similarities):.3f} - {max(all_similarities):.3f}")
        
        print(f"\n🎯 Style Consistency Distribution:")
        for level, count in consistency_levels.items():
            percentage = (count / len(all_results)) * 100
            print(f"   {level} consistency: {count}/{len(all_results)} styles ({percentage:.1f}%)")
        
        # Individual style summaries
        print(f"\n📋 Individual Style Results:")
        for result in all_results:
            stats = result["consistency_stats"]
            print(f"   {result['style_name']}:")
            print(f"      Mean: {stats['mean_similarity']:.3f}, Range: {stats['similarity_range']:.3f}, Level: {result['consistency_level']}")
        
        # Save comprehensive results
        results_file = f"style_verification/style_aware_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        os.makedirs("style_verification", exist_ok=True)
        
        comprehensive_results = {
            "configuration": {
                "num_styles_tested": len(all_results),
                "tiles_per_style": 4,
                "total_tiles": len(all_similarities),
                "test_type": "style_aware_verification"
            },
            "style_results": all_results,
            "overall_statistics": {
                "average_similarity": sum(all_similarities) / len(all_similarities),
                "similarity_std": (sum((s - sum(all_similarities)/len(all_similarities))**2 for s in all_similarities) / len(all_similarities))**0.5,
                "min_similarity": min(all_similarities),
                "max_similarity": max(all_similarities),
                "consistency_distribution": consistency_levels
            },
            "timestamp": datetime.now().isoformat()
        }
        
        with open(results_file, 'w') as f:
            json.dump(comprehensive_results, f, indent=2)
        
        print(f"\n📁 Results saved to: {results_file}")
        print("✅ Style-aware verification completed!")
        
        return comprehensive_results


async def main():
    """Main function to run style-aware verification or optimization."""
    print("🎨 Style-Aware System")
    print("Each style has 4 tiles from the same original 1024x1024 image")
    print("\nChoose test type:")
    print("1. Verification (test consistency across tiles)")
    print("2. Optimization (use tiles for DSPy optimization)")

    test_type = input("Enter choice (1-2): ").strip()

    if test_type not in ["1", "2"]:
        print("❌ Invalid choice")
        return

    print("\nChoose the number of styles to test:")
    print("1. 2 styles (8 images, ~$0.32)")
    print("2. 3 styles (12 images, ~$0.48)")
    print("3. 5 styles (20 images, ~$0.80)")
    print("4. Custom number")

    choice = input("Enter choice (1-4): ").strip()

    if choice == "1":
        num_styles = 2
    elif choice == "2":
        num_styles = 3
    elif choice == "3":
        num_styles = 5
    elif choice == "4":
        try:
            num_styles = int(input("Enter number of styles: ").strip())
            if num_styles < 1 or num_styles > 10:
                print("❌ Please enter a number between 1 and 10")
                return
        except ValueError:
            print("❌ Invalid number")
            return
    else:
        print("❌ Invalid choice")
        return

    system = StyleAwareVerificationSystem()

    if test_type == "1":
        await system.run_style_aware_verification(num_styles)
    else:
        await system.run_style_aware_optimization(num_styles)


if __name__ == "__main__":
    asyncio.run(main())
