"""
Test if the Flask server is running by making HTTP requests.
"""

import requests
import time

def test_server():
    """Test if the Flask server is responding."""
    print("🧪 Testing Flask Server")
    print("=" * 30)
    
    base_url = "http://localhost:5000"
    
    # Test endpoints
    endpoints = [
        ("/", "Main page"),
        ("/health", "Health check"),
        ("/api/templates", "Templates API")
    ]
    
    for endpoint, description in endpoints:
        url = f"{base_url}{endpoint}"
        print(f"\n🔍 Testing: {description}")
        print(f"📍 URL: {url}")
        
        try:
            response = requests.get(url, timeout=5)
            print(f"✅ Status: {response.status_code}")
            
            if response.status_code == 200:
                content = response.text[:100] + "..." if len(response.text) > 100 else response.text
                print(f"📄 Content: {content}")
            else:
                print(f"❌ Error: {response.text}")
                
        except requests.exceptions.ConnectionError:
            print("❌ Connection failed - server not running")
        except requests.exceptions.Timeout:
            print("⏰ Request timed out")
        except Exception as e:
            print(f"❌ Error: {e}")
    
    # Test port 5001 as well (for the test Flask app)
    print(f"\n🔍 Testing test server on port 5001...")
    try:
        response = requests.get("http://localhost:5001", timeout=5)
        print(f"✅ Test server status: {response.status_code}")
        if response.status_code == 200:
            print(f"📄 Test server content: {response.text[:50]}...")
    except requests.exceptions.ConnectionError:
        print("❌ Test server not running")
    except Exception as e:
        print(f"❌ Test server error: {e}")

if __name__ == '__main__':
    test_server()
