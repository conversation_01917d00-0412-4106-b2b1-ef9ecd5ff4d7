"""
Ground truth validation system using Google search results.
Compares CoT-generated ideas against real-world information from search results.
"""

import sys
import json
import time
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass
from pydantic import BaseModel, Field

# Add src to path for imports
src_path = Path(__file__).parent.parent
if str(src_path) not in sys.path:
    sys.path.insert(0, str(src_path))

from cot_system.cot_idea_generator import CoTIdeaSystem, IdeaStructure


class SearchResult(BaseModel):
    """Individual search result from Google."""
    title: str
    url: str
    snippet: str
    position: int


class GroundTruthData(BaseModel):
    """Ground truth data collected from search results."""
    query: str
    search_results: List[SearchResult]
    key_concepts: List[str] = Field(default_factory=list)
    common_themes: List[str] = Field(default_factory=list)
    expert_recommendations: List[str] = Field(default_factory=list)
    timestamp: str


class ValidationScore(BaseModel):
    """Validation score comparing CoT ideas against ground truth."""
    concept_overlap: float = Field(ge=0.0, le=1.0, description="Overlap of key concepts")
    theme_alignment: float = Field(ge=0.0, le=1.0, description="Alignment with common themes")
    recommendation_quality: float = Field(ge=0.0, le=1.0, description="Quality of recommendations")
    novelty_score: float = Field(ge=0.0, le=1.0, description="Novel insights not in search results")
    overall_score: float = Field(ge=0.0, le=1.0, description="Overall validation score")
    detailed_feedback: str = ""


class GroundTruthValidator:
    """Validates CoT-generated ideas against Google search results."""
    
    def __init__(self, max_results: int = 10):
        """
        Initialize the validator.
        
        Args:
            max_results: Maximum number of search results to analyze
        """
        self.max_results = max_results
        self.cot_system = None
        
    def initialize_cot_system(self) -> bool:
        """Initialize the CoT system for comparison."""
        try:
            self.cot_system = CoTIdeaSystem()
            return self.cot_system.generator is not None
        except Exception as e:
            print(f"❌ Failed to initialize CoT system: {e}")
            return False
    
    def search_google(self, query: str, num_results: int = None) -> List[SearchResult]:
        """
        Search Google for the given query.
        
        Args:
            query: Search query
            num_results: Number of results to return (defaults to max_results)
            
        Returns:
            List of search results
        """
        if num_results is None:
            num_results = self.max_results
            
        # For now, we'll simulate search results
        # In a real implementation, you would use Google Custom Search API
        print(f"🔍 Searching Google for: '{query}' (top {num_results} results)")
        
        # Simulated search results - replace with actual Google API call
        simulated_results = [
            SearchResult(
                title=f"Expert Guide: {query.title()}",
                url=f"https://example.com/guide-{i}",
                snippet=f"Comprehensive guide covering {query} with expert insights and best practices...",
                position=i+1
            )
            for i in range(min(num_results, 5))
        ]
        
        print(f"✅ Found {len(simulated_results)} search results")
        return simulated_results
    
    def extract_ground_truth(self, search_results: List[SearchResult], query: str) -> GroundTruthData:
        """
        Extract ground truth data from search results.
        
        Args:
            search_results: List of search results
            query: Original search query
            
        Returns:
            Ground truth data
        """
        print("📊 Extracting ground truth from search results...")
        
        # Extract key concepts from titles and snippets
        key_concepts = []
        common_themes = []
        expert_recommendations = []
        
        for result in search_results:
            # Simple keyword extraction (in real implementation, use NLP)
            text = f"{result.title} {result.snippet}".lower()
            
            # Extract potential concepts
            if "optimization" in text:
                key_concepts.append("optimization")
            if "performance" in text:
                key_concepts.append("performance")
            if "best practices" in text:
                common_themes.append("best practices")
            if "implementation" in text:
                common_themes.append("implementation")
            if "should" in text or "recommend" in text:
                expert_recommendations.append(f"Recommendation from {result.title}")
        
        # Remove duplicates
        key_concepts = list(set(key_concepts))
        common_themes = list(set(common_themes))
        
        ground_truth = GroundTruthData(
            query=query,
            search_results=search_results,
            key_concepts=key_concepts,
            common_themes=common_themes,
            expert_recommendations=expert_recommendations,
            timestamp=time.strftime("%Y-%m-%d %H:%M:%S")
        )
        
        print(f"✅ Extracted {len(key_concepts)} key concepts, {len(common_themes)} themes")
        return ground_truth
    
    def validate_ideas(self, ideas: IdeaStructure, ground_truth: GroundTruthData) -> ValidationScore:
        """
        Validate CoT-generated ideas against ground truth.
        
        Args:
            ideas: CoT-generated ideas
            ground_truth: Ground truth data from search results
            
        Returns:
            Validation score
        """
        print("🔍 Validating CoT ideas against ground truth...")
        
        # Extract concepts from CoT ideas
        cot_text = f"{ideas.topic} {ideas.summary} " + " ".join([bp.content for bp in ideas.bullet_points])
        cot_concepts = []
        
        # Simple concept extraction (improve with NLP in real implementation)
        for concept in ground_truth.key_concepts:
            if concept.lower() in cot_text.lower():
                cot_concepts.append(concept)
        
        # Calculate scores
        concept_overlap = len(cot_concepts) / max(len(ground_truth.key_concepts), 1)
        
        # Theme alignment (simplified)
        theme_matches = 0
        for theme in ground_truth.common_themes:
            if theme.lower() in cot_text.lower():
                theme_matches += 1
        theme_alignment = theme_matches / max(len(ground_truth.common_themes), 1)
        
        # Recommendation quality (based on actionability)
        actionable_items = len([bp for bp in ideas.bullet_points if bp.priority in ["high", "medium"]])
        recommendation_quality = min(actionable_items / 5.0, 1.0)  # Normalize to max 5 items
        
        # Novelty score (concepts not in ground truth)
        all_gt_text = " ".join([r.snippet for r in ground_truth.search_results]).lower()
        novel_concepts = 0
        for bp in ideas.bullet_points:
            if bp.content.lower() not in all_gt_text:
                novel_concepts += 1
        novelty_score = min(novel_concepts / len(ideas.bullet_points), 1.0) if ideas.bullet_points else 0.0
        
        # Overall score (weighted average)
        overall_score = (
            concept_overlap * 0.3 +
            theme_alignment * 0.25 +
            recommendation_quality * 0.25 +
            novelty_score * 0.2
        )
        
        # Generate detailed feedback
        feedback = f"""
        Concept Overlap: {concept_overlap:.2f} ({len(cot_concepts)}/{len(ground_truth.key_concepts)} concepts matched)
        Theme Alignment: {theme_alignment:.2f} ({theme_matches}/{len(ground_truth.common_themes)} themes matched)
        Recommendation Quality: {recommendation_quality:.2f} ({actionable_items} actionable items)
        Novelty Score: {novelty_score:.2f} ({novel_concepts} novel concepts)
        
        Matched Concepts: {', '.join(cot_concepts) if cot_concepts else 'None'}
        Ground Truth Concepts: {', '.join(ground_truth.key_concepts)}
        """
        
        validation_score = ValidationScore(
            concept_overlap=concept_overlap,
            theme_alignment=theme_alignment,
            recommendation_quality=recommendation_quality,
            novelty_score=novelty_score,
            overall_score=overall_score,
            detailed_feedback=feedback.strip()
        )
        
        print(f"✅ Validation complete - Overall Score: {overall_score:.2f}")
        return validation_score
    
    def run_validation(self, topic: str, context: str = "", num_results: int = None) -> Tuple[IdeaStructure, GroundTruthData, ValidationScore]:
        """
        Run complete validation process.
        
        Args:
            topic: Topic to validate
            context: Additional context
            num_results: Number of search results to use
            
        Returns:
            Tuple of (generated ideas, ground truth, validation score)
        """
        print(f"🧪 Running Ground Truth Validation")
        print(f"📝 Topic: {topic}")
        print(f"🎯 Context: {context}")
        print("=" * 60)
        
        # Initialize CoT system if needed
        if not self.cot_system:
            if not self.initialize_cot_system():
                raise RuntimeError("Failed to initialize CoT system")
        
        # Generate ideas with CoT
        print("🧠 Generating ideas with CoT system...")
        ideas = self.cot_system.generate_ideas(topic=topic, context=context)
        print(f"✅ Generated {len(ideas.bullet_points)} bullet points")
        
        # Search for ground truth
        search_query = f"{topic} {context}".strip()
        search_results = self.search_google(search_query, num_results)
        
        # Extract ground truth
        ground_truth = self.extract_ground_truth(search_results, search_query)
        
        # Validate ideas
        validation_score = self.validate_ideas(ideas, ground_truth)
        
        return ideas, ground_truth, validation_score
    
    def save_validation_results(self, ideas: IdeaStructure, ground_truth: GroundTruthData, 
                              validation_score: ValidationScore, output_file: str):
        """Save validation results to file."""
        results = {
            "ideas": ideas.dict(),
            "ground_truth": ground_truth.dict(),
            "validation_score": validation_score.dict(),
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S")
        }
        
        output_path = Path(output_file)
        output_path.parent.mkdir(parents=True, exist_ok=True)
        
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)
        
        print(f"💾 Results saved to: {output_path}")


def main():
    """Main function for testing the ground truth validator."""
    validator = GroundTruthValidator(max_results=5)
    
    # Test validation
    topic = "improving web application performance"
    context = "focus on frontend optimization techniques"
    
    try:
        ideas, ground_truth, score = validator.run_validation(topic, context)
        
        print("\n" + "="*60)
        print("📊 VALIDATION RESULTS")
        print("="*60)
        print(f"Overall Score: {score.overall_score:.2f}")
        print(f"Concept Overlap: {score.concept_overlap:.2f}")
        print(f"Theme Alignment: {score.theme_alignment:.2f}")
        print(f"Recommendation Quality: {score.recommendation_quality:.2f}")
        print(f"Novelty Score: {score.novelty_score:.2f}")
        print("\nDetailed Feedback:")
        print(score.detailed_feedback)
        
        # Save results
        validator.save_validation_results(
            ideas, ground_truth, score,
            "results/ground_truth_validation/test_validation.json"
        )
        
    except Exception as e:
        print(f"❌ Validation failed: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
