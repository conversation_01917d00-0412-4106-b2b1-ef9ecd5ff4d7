# Async Implementation for DSPy Style Extraction Pipeline

## Overview

The DSPy style extraction pipeline has been enhanced with asynchronous functionality to improve performance and enable concurrent processing. This document outlines the changes made and how to use the new async features.

## Key Changes

### 1. Core Modules Updated

#### `main.py`
- Added `asyncio` import
- Created async versions of key functions:
  - `evaluate_predictors_async()` - Async evaluation of predictors
  - `run_optimization_comparison_async()` - Async optimization comparison
  - `generate_new_examples_async()` - Async example generation
  - `main_async()` - Async main function
- Maintained backward compatibility with sync wrapper functions

#### `evaluation.py`
- Added `run_evaluation_async()` function
- Uses `asyncio.get_event_loop().run_in_executor()` to run DSPy evaluations without blocking

#### `utils/data_utils.py`
- Added `generate_examples_from_images_async()` function
- Implements concurrent image processing with semaphore-based rate limiting
- Processes multiple images concurrently while respecting API limits

### 2. Async Function Features

#### Concurrent Processing
- **Image Generation**: Processes multiple images concurrently with configurable semaphore (default: 5 concurrent requests)
- **Optimization**: Runs optimization steps in thread pools to avoid blocking
- **Evaluation**: Executes evaluations asynchronously

#### Rate Limiting
- Uses `asyncio.Semaphore(5)` to limit concurrent API requests
- Prevents overwhelming the OpenAI API while maximizing throughput

#### Error Handling
- Graceful handling of exceptions in concurrent tasks
- Failed tasks don't stop the entire process
- Detailed error reporting for debugging

## Usage Examples

### Basic Async Evaluation
```python
import asyncio
from main import evaluate_predictors_async, setup_dspy, create_predictor, load_training_data

async def run_evaluation():
    setup_dspy()
    lm = dspy.LM("openai/gpt-4o-mini", cache_in_memory=False)
    dspy.configure(lm=lm)
    
    predictor = create_predictor()
    trainset, devset = load_training_data()
    
    # Run async evaluation
    result = await evaluate_predictors_async(predictor, None, devset)
    print(f"Evaluation result: {result}")

# Run the async function
asyncio.run(run_evaluation())
```

### Async Optimization Comparison
```python
async def run_optimization():
    # Setup code...
    
    results = await run_optimization_comparison_async(
        predictor, trainset, devset,
        optimizers_to_run=["labeled_fewshot", "bootstrap_fewshot"],
        num_threads=24, display_table=5
    )
    return results
```

### Concurrent Example Generation
```python
async def generate_examples():
    # Setup code...
    
    examples = await generate_new_examples_async(
        predictor,
        "examples/groovjones_styles/tile_images_list.json",
        "examples/async_generated_examples.jsonl"
    )
    return examples
```

### Running Multiple Tasks Concurrently
```python
async def run_concurrent_tasks():
    # Setup code...
    
    # Run multiple operations concurrently
    tasks = [
        evaluate_predictors_async(predictor, None, devset1),
        evaluate_predictors_async(predictor, None, devset2),
        generate_new_examples_async(predictor, images_file, output_file)
    ]
    
    results = await asyncio.gather(*tasks)
    return results
```

## Performance Benefits

### 1. Concurrent Processing
- **Image Generation**: Process multiple images simultaneously instead of sequentially
- **API Utilization**: Better utilization of OpenAI API rate limits
- **I/O Efficiency**: Non-blocking I/O operations

### 2. Scalability
- **Configurable Concurrency**: Adjust semaphore limits based on API quotas
- **Resource Management**: Efficient use of system resources
- **Parallel Optimization**: Run multiple optimization strategies concurrently

### 3. Responsiveness
- **Non-blocking Operations**: UI/CLI remains responsive during long operations
- **Progress Monitoring**: Real-time progress updates
- **Graceful Cancellation**: Ability to cancel long-running operations

## Backward Compatibility

All original synchronous functions remain available:
- `evaluate_predictors()` - Calls `evaluate_predictors_async()` internally
- `run_optimization_comparison()` - Calls `run_optimization_comparison_async()` internally
- `generate_new_examples()` - Calls `generate_new_examples_async()` internally
- `main()` - Calls `main_async()` internally

## Testing

Use the provided `test_async.py` script to test async functionality:

```bash
python test_async.py
```

The test script includes:
- Basic async evaluation test
- Async optimization comparison test
- Concurrent task execution test
- Async example generation test (if image files exist)

## Configuration

### Concurrency Limits
Adjust the semaphore limit in `generate_examples_from_images_async()`:
```python
semaphore = asyncio.Semaphore(5)  # Adjust based on your API limits
```

### Thread Pool Settings
Modify thread pool usage in optimization functions:
```python
# In run_optimization_comparison_async()
optimized_predictor = await loop.run_in_executor(None, optimization_function)
```

## Best Practices

1. **API Rate Limits**: Respect OpenAI API rate limits by adjusting semaphore values
2. **Error Handling**: Always use try-catch blocks in async functions
3. **Resource Cleanup**: Ensure proper cleanup of resources in async contexts
4. **Progress Monitoring**: Use progress indicators for long-running operations
5. **Cancellation**: Implement proper cancellation handling for user interrupts

## Future Enhancements

Potential areas for further async improvements:
- Async file I/O operations
- Streaming results for real-time updates
- WebSocket support for real-time monitoring
- Distributed processing across multiple machines
- Advanced retry mechanisms with exponential backoff
