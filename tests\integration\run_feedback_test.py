"""
Simple runner for the feedback optimization test.
Choose between simple feedback test or complete optimization pipeline.
"""

import asyncio
import sys


def print_menu():
    """Print the menu options."""
    print("\n🎯 Feedback Optimization Test Menu")
    print("=" * 50)
    print("1. Simple Feedback Test (2 images, ~$0.08)")
    print("2. Basic Optimization Pipeline (2 images, ~$0.08)")
    print("3. Enhanced Optimization (10-20 images, ~$0.40-0.80)")
    print("4. Exit")
    print("=" * 50)


async def run_simple_test():
    """Run the simple feedback test."""
    print("\n🚀 Running Simple Feedback Test...")
    from simple_feedback_test import main as simple_main
    await simple_main()


async def run_complete_pipeline():
    """Run the basic optimization pipeline."""
    print("\n🚀 Running Basic Optimization Pipeline...")
    from feedback_with_optimization import main as complete_main
    await complete_main()


async def run_enhanced_pipeline():
    """Run the enhanced optimization pipeline."""
    print("\n🚀 Running Enhanced Optimization Pipeline...")
    from enhanced_feedback_optimization import main as enhanced_main
    await enhanced_main()


async def main():
    """Main menu function."""
    print("🎨 DSPy Style Analysis Feedback Optimization")
    print("This tool tests style analysis by generating images and optimizing based on feedback.")
    print("⚠️  Note: This uses DALL-E 3 which costs money (~$0.04 per image)")
    
    while True:
        print_menu()
        
        try:
            choice = input("\nEnter your choice (1-4): ").strip()

            if choice == "1":
                confirm = input("\n⚠️  This will generate 2 images (~$0.08). Continue? (y/n): ").strip().lower()
                if confirm == 'y':
                    await run_simple_test()
                else:
                    print("❌ Cancelled")

            elif choice == "2":
                confirm = input("\n⚠️  This will generate 2 images and run optimization (~$0.08). Continue? (y/n): ").strip().lower()
                if confirm == 'y':
                    await run_complete_pipeline()
                else:
                    print("❌ Cancelled")

            elif choice == "3":
                print("\n⚠️  Enhanced optimization uses 10-20 images (~$0.40-0.80)")
                print("   This provides better optimization results with more diverse training data.")
                confirm = input("   Continue? (y/n): ").strip().lower()
                if confirm == 'y':
                    await run_enhanced_pipeline()
                else:
                    print("❌ Cancelled")

            elif choice == "4":
                print("👋 Goodbye!")
                break

            else:
                print("❌ Invalid choice. Please enter 1, 2, 3, or 4.")
                
        except KeyboardInterrupt:
            print("\n\n👋 Goodbye!")
            break
        except Exception as e:
            print(f"❌ Error: {e}")
            
        # Ask if user wants to continue
        if choice in ["1", "2", "3"]:
            continue_choice = input("\nWould you like to run another test? (y/n): ").strip().lower()
            if continue_choice != 'y':
                print("👋 Goodbye!")
                break


if __name__ == "__main__":
    asyncio.run(main())
