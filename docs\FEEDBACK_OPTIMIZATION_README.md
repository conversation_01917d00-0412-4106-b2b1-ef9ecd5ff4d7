# Feedback Optimization System for DSPy Style Analysis

## Overview

This system creates a feedback loop to improve DSPy style analysis by:

1. **Taking generated style examples** (4 per style from your tile images)
2. **Generating new images** using DALL-E 3 based on those style analyses
3. **Analyzing the generated images** with the DSPy predictor
4. **Comparing expected vs actual results** to create feedback
5. **Using feedback to optimize** the DSPy predictor with LabeledFewShot

## Files

### Core Scripts

- **`run_feedback_test.py`** - Main menu to choose test type
- **`simple_feedback_test.py`** - Basic feedback test (2 images)
- **`feedback_with_optimization.py`** - Complete pipeline with DSPy optimization
- **`feedback_optimization_test.py`** - Advanced version with detailed analysis

### How It Works

```mermaid
graph TD
    A[Style Examples from Tiles] --> B[Generate Images with DALL-E]
    B --> C[Analyze Generated Images with DSPy]
    C --> D[Compare Expected vs Actual]
    D --> E[Calculate Similarity Score]
    E --> F[Create Optimization Examples]
    F --> G[Optimize DSPy Predictor]
    G --> H[Test Optimized Predictor]
    H --> I[Measure Improvement]
```

## Usage

### Quick Start

```bash
python run_feedback_test.py
```

Choose option 1 for simple test or option 2 for complete optimization.

### Manual Execution

#### Simple Feedback Test
```bash
python simple_feedback_test.py
```

#### Complete Optimization Pipeline
```bash
python feedback_with_optimization.py
```

## Cost Considerations

- **DALL-E 3**: ~$0.04 per 1024x1024 image
- **Simple test**: 2 images = ~$0.08
- **Complete pipeline**: 2 images = ~$0.08
- **GPT-4o-mini**: Very low cost for analysis

## Expected Results

### Similarity Scoring

The system compares style analyses using keyword overlap in key fields:
- `visual_composition`
- `color_characteristics` 
- `mark_making_and_technique`
- `form_and_structure`

**Score Interpretation:**
- `0.0-0.2`: Poor match
- `0.2-0.4`: Fair match  
- `0.4-0.6`: Good match
- `0.6+`: Excellent match

### Optimization Impact

The LabeledFewShot optimization should improve similarity scores by:
- Teaching the predictor the expected style analysis format
- Reinforcing consistent terminology and structure
- Improving accuracy for similar visual styles

## Output Files

### Simple Test
- `feedback_test_simple/`
  - `generated_*.png` - Generated images
  - `feedback_results_*.json` - Analysis results
  - `optimization_examples.jsonl` - Examples for future optimization

### Complete Pipeline
- `feedback_optimization/`
  - `feedback_*.png` - Generated images
  - `optimization_results_*.json` - Complete results with before/after comparison

## Example Workflow

1. **Start with tile examples**: 208 examples from your style tiles
2. **Select 2 for testing**: To minimize costs
3. **Generate images**: DALL-E creates images from style descriptions
4. **Analyze with DSPy**: Original predictor analyzes generated images
5. **Calculate similarity**: Compare expected vs actual style analysis
6. **Create training data**: Use expected analyses as training examples
7. **Optimize predictor**: LabeledFewShot learns from feedback
8. **Test improvement**: Re-analyze same images with optimized predictor
9. **Measure gains**: Compare before/after similarity scores

## Sample Results Format

```json
{
  "test_summary": {
    "average_original": 0.245,
    "average_optimized": 0.387,
    "average_improvement": 0.142,
    "num_tests": 2
  },
  "test_results": [
    {
      "style_name": "16-Bit_Retro_Aesthetic",
      "original_similarity": 0.234,
      "optimized_similarity": 0.456,
      "improvement": 0.222
    }
  ]
}
```

## Troubleshooting

### Common Issues

1. **OpenAI API Key**: Ensure `OPENAI_API_KEY` environment variable is set
2. **File Paths**: Make sure `examples/groovjones_styles/tile_examples.jsonl` exists
3. **Memory**: Large images may require more RAM
4. **Rate Limits**: DALL-E has rate limits; script includes error handling

### Error Messages

- `Examples file not found`: Check that tile_examples.jsonl exists
- `Failed to download image`: Network or API issue
- `Error generating image`: DALL-E API problem or invalid prompt

## Extending the System

### Add More Examples
Modify `num_examples` parameter in scripts to test more styles (costs more).

### Different Models
Change the DSPy language model:
```python
lm = dspy.LM("openai/gpt-4", cache_in_memory=False)  # More expensive but better
```

### Custom Similarity Metrics
Modify `calculate_style_similarity()` to use:
- Semantic similarity (embeddings)
- Color palette comparison
- Visual feature extraction

### Advanced Optimization
Try different DSPy optimizers:
- `BootstrapFewShot`
- `COPRO`
- `MIPROv2`

## Next Steps

1. **Run initial test** with 2 examples
2. **Analyze results** to see baseline similarity scores
3. **Measure optimization impact** 
4. **Scale up** if results are promising
5. **Integrate** optimized predictor into main pipeline

## Benefits

- **Validates style analysis quality** by generating actual images
- **Provides quantitative feedback** on predictor performance  
- **Enables continuous improvement** through optimization
- **Creates better training data** from real-world feedback
- **Improves consistency** across different styles

This feedback system helps ensure your DSPy style analysis produces descriptions that actually generate the intended visual styles!
