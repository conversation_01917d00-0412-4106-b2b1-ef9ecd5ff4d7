"""
Debug script to test the web application step by step.
"""

import sys
import os
from pathlib import Path

def main():
    print("🔍 Debugging Web Application")
    print("=" * 40)
    
    # Step 1: Check current directory
    current_dir = Path.cwd()
    print(f"📁 Current directory: {current_dir}")
    
    # Step 2: Check if we're in the right place
    repo_root = Path(__file__).parent
    src_path = repo_root / 'src'
    web_path = src_path / 'web'
    
    print(f"📁 Repository root: {repo_root}")
    print(f"📁 Source path: {src_path}")
    print(f"📁 Web path: {web_path}")
    
    # Step 3: Check if files exist
    app_file = web_path / 'app.py'
    cot_file = src_path / 'cot_system' / 'cot_idea_generator.py'
    
    print(f"📄 App file exists: {app_file.exists()}")
    print(f"📄 CoT file exists: {cot_file.exists()}")
    
    # Step 4: Test imports
    if str(src_path) not in sys.path:
        sys.path.insert(0, str(src_path))
        print("✅ Added src to Python path")
    
    try:
        print("📦 Testing CoT import...")
        from cot_system.cot_idea_generator import CoTIdeaSystem
        print("✅ CoT import successful")
        
        print("📦 Testing Flask import...")
        from flask import Flask
        print("✅ Flask import successful")
        
        # Step 5: Test CoT system
        print("🔄 Testing CoT system initialization...")
        system = CoTIdeaSystem()
        print("✅ CoT system works")
        
        # Step 6: Try to import the web app
        print("📦 Testing web app import...")
        
        # Change to web directory
        os.chdir(str(web_path))
        print(f"📁 Changed to: {Path.cwd()}")
        
        # Import the app
        sys.path.insert(0, str(web_path))
        from app import app, initialize_system
        print("✅ Web app import successful")
        
        # Test initialization
        print("🔄 Testing web app initialization...")
        if initialize_system():
            print("✅ Web app initialization successful")
            
            print("🚀 Starting Flask server...")
            print("🌐 Server will be available at: http://localhost:5000")
            print("🛑 Press Ctrl+C to stop")
            
            # Start the server
            app.run(debug=True, host='127.0.0.1', port=5000, use_reloader=False)
        else:
            print("❌ Web app initialization failed")
            
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()
