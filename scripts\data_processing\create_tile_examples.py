#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to create DSPy examples from the downloaded image tiles.
"""

import json
import os
from pathlib import Path
import dspy
from utils.data_utils import generate_examples_from_images
from main import setup_dspy, ExtractStyleSignature

def create_image_list_from_tiles():
    """Create a JSON file with all tile image paths."""
    images_dir = Path("examples/groovjones_styles/images")
    
    # Get all tile files (not the original images)
    tile_files = []
    for file_path in images_dir.glob("*_tile_*.png"):
        # Convert to relative path from project root
        relative_path = str(file_path)
        tile_files.append(relative_path)
    
    # Sort for consistent ordering
    tile_files.sort()
    
    # Create the JSON structure expected by get_images_links
    image_data = []
    for tile_path in tile_files:
        image_data.append({
            "image_link": tile_path,
            "description": f"Style tile from {Path(tile_path).stem}"
        })
    
    # Save to JSON file
    output_file = "examples/groovjones_styles/tile_images_list.json"
    with open(output_file, 'w') as f:
        json.dump(image_data, f, indent=2)
    
    print(f"✅ Created {output_file} with {len(image_data)} tile images")
    return output_file, len(image_data)

def main():
    """Main function to create examples from tiles."""
    print("🚀 Creating DSPy examples from image tiles...")

    # Create the image list file
    images_file, num_images = create_image_list_from_tiles()

    # Setup DSPy with proper language model
    print("🔧 Setting up DSPy...")
    setup_dspy()

    # Configure OpenAI model (same as main.py)
    lm = dspy.LM("openai/gpt-4o-mini", cache_in_memory=False)
    dspy.configure(lm=lm)

    # Create a predictor for generating examples
    predictor = dspy.Predict(ExtractStyleSignature)

    # Generate examples
    print(f"🎨 Generating examples from {num_images} tile images...")
    output_file = "examples/groovjones_styles/tile_examples.jsonl"

    examples = generate_examples_from_images(
        predictor=predictor,
        images_file=images_file,
        output_file=output_file
    )

    print(f"✅ Generated {len(examples)} examples and saved to {output_file}")

    return examples

if __name__ == "__main__":
    main()
