"""
Evaluation utilities for style extraction pipeline.
"""

import json
import asyncio


def style_score(example, pred, trace=None) -> float:
    """
    Calculate the style score based on verification checks in the prediction metadata.
    
    Args:
        example: The input example (unused but required by DSPy interface)
        pred: The prediction object containing style and metadata
        
    Returns:
        Float score between 0.0 and 1.0
    """
    try:
        # Parse metadata if it's a string
        if isinstance(pred.metadata, str):
            metadata = json.loads(pred.metadata)
        else:
            metadata = pred.metadata
        
        # Extract the overall score
        verification_checks = metadata.get("verification_checks", {})
        if isinstance(verification_checks, dict):
            score = verification_checks.get("overall_score", 0.0)
        else:
            print(f"Warning: verification_checks is not a dict: {type(verification_checks)}")
            return 0.0
            
        # Ensure score is a valid float between 0 and 1
        if isinstance(score, (int, float)) and 0 <= score <= 1:
            return float(score)
        else:
            print(f"Warning: Invalid score value: {score}")
            return 0.0
            
    except (json.JSONDecode<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>rror) as e:
        print(f"Error parsing metadata: {e}")
        print(f"Metadata type: {type(pred.metadata)}")
        if hasattr(pred, 'metadata'):
            print(f"Metadata content (first 200 chars): {str(pred.metadata)[:200]}")
        return 0.0


def run_evaluation(predictor, devset, style_score_func, num_threads=24, display_table=5, **kwargs):
    """
    Run evaluation on a predictor using the given dataset and metric.

    Args:
        predictor: DSPy predictor to evaluate
        devset: Development dataset
        style_score_func: Scoring function
        num_threads: Number of threads for parallel evaluation
        display_table: Number of rows to display in results table

    Returns:
        Evaluation score
    """
    import dspy

    kwargs = dict(num_threads=num_threads, display_progress=True, display_table=display_table, return_all_scores=True)
    evaluate = dspy.Evaluate(devset=devset, metric=style_score_func, **kwargs)
    return evaluate(predictor)


async def run_evaluation_async(predictor, devset, style_score_func, num_threads=24, display_table=5, **kwargs):
    """
    Run evaluation on a predictor using the given dataset and metric asynchronously.

    Args:
        predictor: DSPy predictor to evaluate
        devset: Development dataset
        style_score_func: Scoring function
        num_threads: Number of threads for parallel evaluation
        display_table: Number of rows to display in results table

    Returns:
        Evaluation score
    """
    import dspy

    # Run the synchronous evaluation in a thread pool to avoid blocking
    loop = asyncio.get_event_loop()

    def _run_sync_evaluation():
        kwargs_dict = dict(num_threads=num_threads, display_progress=True, display_table=display_table, return_all_scores=True)
        evaluate = dspy.Evaluate(devset=devset, metric=style_score_func, **kwargs_dict)
        return evaluate(predictor)

    return await loop.run_in_executor(None, _run_sync_evaluation)
