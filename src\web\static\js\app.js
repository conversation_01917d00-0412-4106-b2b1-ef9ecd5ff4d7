// Chain of Thought Idea Generator Frontend

class CoTIdeaApp {
    constructor() {
        this.currentResults = null;
        this.init();
    }

    init() {
        this.bindEvents();
        this.loadTemplates();
    }

    bindEvents() {
        // Generate button
        document.getElementById('generateBtn').addEventListener('click', () => {
            this.generateIdeas();
        });

        // Save button
        document.getElementById('saveBtn').addEventListener('click', () => {
            this.saveResults();
        });

        // Enter key in topic field
        document.getElementById('topic').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.generateIdeas();
            }
        });

        // Template selection
        document.getElementById('template').addEventListener('change', (e) => {
            const contextField = document.getElementById('context');
            if (e.target.value) {
                // Clear custom context when template is selected
                contextField.placeholder = 'Template context will be used automatically...';
                contextField.disabled = true;
            } else {
                contextField.placeholder = 'Add any additional context or constraints...';
                contextField.disabled = false;
            }
        });
    }

    async loadTemplates() {
        try {
            const response = await fetch('/api/templates');
            const data = await response.json();
            
            if (data.templates) {
                const templateSelect = document.getElementById('template');
                
                data.templates.forEach(template => {
                    const option = document.createElement('option');
                    option.value = template.id;
                    option.textContent = template.name;
                    option.title = template.description;
                    templateSelect.appendChild(option);
                });
            }
        } catch (error) {
            console.error('Error loading templates:', error);
        }
    }

    async generateIdeas() {
        const topic = document.getElementById('topic').value.trim();
        const template = document.getElementById('template').value;
        const context = document.getElementById('context').value.trim();

        if (!topic) {
            this.showError('Please enter a topic');
            return;
        }

        this.showLoading();
        this.hideError();
        this.hideResults();

        try {
            const response = await fetch('/api/generate', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    topic: topic,
                    template: template,
                    context: context
                })
            });

            const data = await response.json();

            if (!response.ok) {
                throw new Error(data.error || 'Failed to generate ideas');
            }

            this.currentResults = data;
            this.displayResults(data);
            this.hideLoading();

        } catch (error) {
            console.error('Error generating ideas:', error);
            this.showError(error.message);
            this.hideLoading();
        }
    }

    displayResults(data) {
        // Set topic
        document.getElementById('resultsTopic').textContent = `🎯 ${data.topic}`;

        // Display thinking chain
        this.displayThinkingChain(data.thinking_chain);

        // Display bullet points
        this.displayBulletPoints(data.bullet_points);

        // Display summary
        document.getElementById('summaryText').textContent = data.summary;

        // Display next steps
        this.displayNextSteps(data.next_steps);

        this.showResults();
    }

    displayThinkingChain(thinkingChain) {
        const container = document.getElementById('thinkingSteps');
        container.innerHTML = '';

        thinkingChain.forEach(step => {
            const stepDiv = document.createElement('div');
            stepDiv.className = 'thinking-step';
            
            stepDiv.innerHTML = `
                <div class="step-header">
                    <span class="step-number">Step ${step.step_number}</span>
                    <span class="step-category">${step.category}</span>
                    <span class="confidence">Confidence: ${(step.confidence * 100).toFixed(0)}%</span>
                </div>
                <div class="step-thought">${step.thought}</div>
            `;
            
            container.appendChild(stepDiv);
        });
    }

    displayBulletPoints(bulletPoints) {
        const container = document.getElementById('bulletPointsContainer');
        container.innerHTML = '';

        // Group by category
        const categories = {};
        bulletPoints.forEach(bp => {
            if (!categories[bp.category]) {
                categories[bp.category] = [];
            }
            categories[bp.category].push(bp);
        });

        // Display each category
        Object.entries(categories).forEach(([category, points]) => {
            const categoryDiv = document.createElement('div');
            categoryDiv.className = 'category-section';
            
            const categoryTitle = document.createElement('h4');
            categoryTitle.textContent = `📂 ${category.toUpperCase()}`;
            categoryTitle.style.marginBottom = '15px';
            categoryTitle.style.color = '#667eea';
            categoryDiv.appendChild(categoryTitle);

            // Sort by priority
            const priorityOrder = { 'high': 0, 'medium': 1, 'low': 2 };
            points.sort((a, b) => priorityOrder[a.priority] - priorityOrder[b.priority]);

            points.forEach(bp => {
                const bpDiv = document.createElement('div');
                bpDiv.className = 'bullet-point';
                
                const priorityIcons = {
                    'high': '🔥',
                    'medium': '⭐',
                    'low': '💡'
                };

                bpDiv.innerHTML = `
                    <div class="bp-header">
                        <span class="priority-icon priority-${bp.priority}">${priorityIcons[bp.priority]}</span>
                        <span class="bp-category">${bp.category}</span>
                    </div>
                    <div class="bp-content">${bp.content}</div>
                    ${bp.supporting_evidence.length > 0 ? `
                        <div class="bp-evidence">
                            <h5>Evidence:</h5>
                            <ul>
                                ${bp.supporting_evidence.map(evidence => `<li>${evidence}</li>`).join('')}
                            </ul>
                        </div>
                    ` : ''}
                    ${bp.related_concepts.length > 0 ? `
                        <div class="bp-related">
                            <h5>Related:</h5>
                            <ul>
                                ${bp.related_concepts.map(concept => `<li>${concept}</li>`).join('')}
                            </ul>
                        </div>
                    ` : ''}
                `;
                
                categoryDiv.appendChild(bpDiv);
            });

            container.appendChild(categoryDiv);
        });
    }

    displayNextSteps(nextSteps) {
        const list = document.getElementById('nextStepsList');
        list.innerHTML = '';

        nextSteps.forEach(step => {
            const li = document.createElement('li');
            li.textContent = step;
            list.appendChild(li);
        });
    }

    async saveResults() {
        if (!this.currentResults) {
            this.showError('No results to save');
            return;
        }

        try {
            const filename = `ideas_${this.currentResults.topic.replace(/[^a-zA-Z0-9]/g, '_').toLowerCase()}_${new Date().toISOString().slice(0, 10)}.json`;
            
            const response = await fetch('/api/save', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    ideas: this.currentResults,
                    filename: filename
                })
            });

            const data = await response.json();

            if (!response.ok) {
                throw new Error(data.error || 'Failed to save results');
            }

            // Show success message
            const saveBtn = document.getElementById('saveBtn');
            const originalText = saveBtn.innerHTML;
            saveBtn.innerHTML = '<i class="fas fa-check"></i> Saved!';
            saveBtn.style.background = 'rgba(46, 204, 113, 0.3)';
            
            setTimeout(() => {
                saveBtn.innerHTML = originalText;
                saveBtn.style.background = 'rgba(255,255,255,0.2)';
            }, 2000);

        } catch (error) {
            console.error('Error saving results:', error);
            this.showError(error.message);
        }
    }

    showLoading() {
        document.getElementById('loadingSection').style.display = 'block';
        document.getElementById('generateBtn').disabled = true;
        document.getElementById('generateBtn').innerHTML = '<i class="fas fa-spinner fa-spin"></i> Generating...';
    }

    hideLoading() {
        document.getElementById('loadingSection').style.display = 'none';
        document.getElementById('generateBtn').disabled = false;
        document.getElementById('generateBtn').innerHTML = '<i class="fas fa-magic"></i> Generate Ideas';
    }

    showResults() {
        document.getElementById('resultsSection').style.display = 'block';
    }

    hideResults() {
        document.getElementById('resultsSection').style.display = 'none';
    }

    showError(message) {
        document.getElementById('errorText').textContent = message;
        document.getElementById('errorSection').style.display = 'block';
    }

    hideError() {
        document.getElementById('errorSection').style.display = 'none';
    }
}

// Initialize the app when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new CoTIdeaApp();
});
