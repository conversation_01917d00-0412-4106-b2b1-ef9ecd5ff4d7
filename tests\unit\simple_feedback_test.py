"""
Simple feedback optimization test.
Takes 2 style examples, generates images, analyzes them, and creates feedback for optimization.
"""

import asyncio
import json
import os
import requests
from datetime import datetime
from openai import OpenAI
import dspy
from dspy import Example

# Import our modules
from main import setup_dspy, create_predictor
from utils.data_utils import load_examples


async def generate_image_from_style(style_analysis_json: str, output_path: str) -> str:
    """Generate an image using DALL-E based on style analysis."""
    client = OpenAI()
    
    try:
        # Parse the style analysis
        style_data = json.loads(style_analysis_json)
        core_chars = style_data.get("core_characteristics", {})
        
        # Build prompt from key visual elements
        prompt_parts = []
        
        # Add key characteristics
        for key in ["visual_composition", "color_characteristics", "mark_making_and_technique", "form_and_structure"]:
            if key in core_chars:
                prompt_parts.append(core_chars[key])
        
        # Add subject
        prompt_parts.append("Create an abstract bear head")
        
        # Combine and limit length
        full_prompt = ". ".join(prompt_parts)
        if len(full_prompt) > 1000:
            full_prompt = full_prompt[:1000] + "..."
        
        print(f"🎨 Generating image with prompt: {full_prompt[:150]}...")
        
        # Generate image
        response = client.images.generate(
            model="dall-e-3",
            prompt=full_prompt,
            size="1024x1024",
            quality="standard",
            n=1,
        )
        
        # Download and save
        image_url = response.data[0].url
        image_response = requests.get(image_url)
        
        if image_response.status_code == 200:
            with open(output_path, 'wb') as f:
                f.write(image_response.content)
            print(f"✅ Image saved to: {output_path}")
            return output_path
        else:
            raise Exception(f"Failed to download image: {image_response.status_code}")
            
    except Exception as e:
        print(f"❌ Error generating image: {e}")
        raise


async def analyze_generated_image(predictor, image_path: str) -> dict:
    """Analyze a generated image using our DSPy predictor."""
    instruction = f"Describe the image: {image_path}"
    
    # Run predictor in thread pool
    loop = asyncio.get_event_loop()
    response = await loop.run_in_executor(
        None, 
        lambda: predictor(instruction=instruction)
    )
    
    return {
        "style": response.style,
        "metadata": response.metadata
    }


def calculate_style_similarity(expected_style: str, actual_style: str) -> dict:
    """Calculate similarity between expected and actual style analysis."""
    try:
        expected_data = json.loads(expected_style)
        actual_data = json.loads(actual_style)
        
        # Compare key characteristics
        expected_core = expected_data.get("core_characteristics", {})
        actual_core = actual_data.get("core_characteristics", {})
        
        key_fields = [
            "visual_composition", "color_characteristics", 
            "mark_making_and_technique", "form_and_structure"
        ]
        
        field_scores = []
        for field in key_fields:
            if field in expected_core and field in actual_core:
                # Simple keyword overlap
                expected_words = set(expected_core[field].lower().split())
                actual_words = set(actual_core[field].lower().split())
                
                if expected_words and actual_words:
                    overlap = len(expected_words.intersection(actual_words))
                    total = len(expected_words.union(actual_words))
                    score = overlap / total if total > 0 else 0
                    field_scores.append(score)
        
        overall_score = sum(field_scores) / len(field_scores) if field_scores else 0
        
        return {
            "overall_score": overall_score,
            "field_scores": field_scores,
            "feedback": f"Similarity: {overall_score:.2f} ({'Good' if overall_score > 0.3 else 'Poor'} match)"
        }
        
    except Exception as e:
        return {
            "overall_score": 0.0,
            "field_scores": [],
            "feedback": f"Error: {e}"
        }


async def process_single_example(predictor, example_data: dict, output_dir: str, index: int) -> dict:
    """Process one example through the complete feedback loop."""
    try:
        # Extract data
        original_instruction = example_data["instruction"]
        expected_style = example_data["style"]
        expected_metadata = example_data["metadata"]
        
        # Get style name
        metadata = json.loads(expected_metadata)
        style_name = metadata.get("style_name", f"style_{index}").replace(" ", "_")
        
        print(f"\n🔄 Processing Example {index}: {style_name}")
        
        # Generate image from style
        timestamp = datetime.now().strftime("%H%M%S")
        image_path = os.path.join(output_dir, f"generated_{style_name}_{timestamp}.png")
        
        await generate_image_from_style(expected_style, image_path)
        
        # Analyze generated image
        print(f"🔍 Analyzing generated image...")
        actual_analysis = await analyze_generated_image(predictor, image_path)
        
        # Calculate similarity
        similarity = calculate_style_similarity(expected_style, actual_analysis["style"])
        
        print(f"📊 {similarity['feedback']}")
        
        # Return feedback data
        return {
            "index": index,
            "style_name": style_name,
            "original_instruction": original_instruction,
            "expected_style": expected_style,
            "generated_image": image_path,
            "actual_style": actual_analysis["style"],
            "similarity": similarity,
            "timestamp": timestamp
        }
        
    except Exception as e:
        print(f"❌ Error processing example {index}: {e}")
        return None


async def main():
    """Main function to run the feedback test."""
    print("🚀 Simple Feedback Optimization Test")
    print("=" * 50)
    
    # Setup DSPy
    setup_dspy()
    lm = dspy.LM("openai/gpt-4o-mini", cache_in_memory=False)
    dspy.configure(lm=lm)
    predictor = create_predictor()
    
    # Create output directory
    output_dir = "feedback_test_simple"
    os.makedirs(output_dir, exist_ok=True)
    
    # Load examples
    examples_file = "examples/groovjones_styles/tile_examples.jsonl"
    if not os.path.exists(examples_file):
        print(f"❌ Examples file not found: {examples_file}")
        return
    
    examples = load_examples(examples_file)
    print(f"📚 Loaded {len(examples)} examples")
    
    # Test with first 2 examples
    test_examples = examples[:2]
    print(f"🧪 Testing with {len(test_examples)} examples to minimize costs")
    
    # Process examples
    results = []
    for i, example in enumerate(test_examples, 1):
        result = await process_single_example(predictor, example, output_dir, i)
        if result:
            results.append(result)
    
    # Save results
    if results:
        results_file = os.path.join(output_dir, f"feedback_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json")
        with open(results_file, 'w') as f:
            json.dump(results, f, indent=2)
        
        print(f"\n📁 Results saved to: {results_file}")
        
        # Summary
        avg_score = sum(r["similarity"]["overall_score"] for r in results) / len(results)
        print(f"\n📈 Summary:")
        print(f"   Average Similarity Score: {avg_score:.3f}")
        
        for result in results:
            score = result["similarity"]["overall_score"]
            name = result["style_name"]
            print(f"   {name}: {score:.3f}")
        
        # Create optimization examples
        print(f"\n🎯 Creating optimization examples...")
        opt_examples = []
        for result in results:
            example = Example(
                instruction=result["original_instruction"],
                style=result["expected_style"]
            ).with_inputs('instruction')
            opt_examples.append(example)
        
        print(f"   Created {len(opt_examples)} examples for future optimization")
        
        # Save optimization examples
        opt_file = os.path.join(output_dir, "optimization_examples.jsonl")
        with open(opt_file, 'w') as f:
            for example in opt_examples:
                f.write(json.dumps({
                    "instruction": example.instruction,
                    "style": example.style
                }) + "\n")
        
        print(f"   Saved to: {opt_file}")
        
        print(f"\n✅ Feedback test completed!")
        
    else:
        print("❌ No results generated")


if __name__ == "__main__":
    asyncio.run(main())
