# 🏗️ Repository Structure

This document describes the organized structure of the DSPy Chain of Thought Idea Generator repository.

## 📁 Directory Structure

```
dspy_test/
├── 📂 src/                           # Source Code
│   ├── 🧠 cot_system/               # Chain of Thought System
│   │   ├── __init__.py
│   │   └── cot_idea_generator.py    # Core CoT implementation
│   │
│   ├── 🌐 web/                      # Web Application
│   │   ├── __init__.py
│   │   ├── app.py                   # Flask backend
│   │   ├── templates/
│   │   │   └── index.html           # Main HTML template
│   │   └── static/
│   │       ├── css/
│   │       │   └── style.css        # Styling
│   │       └── js/
│   │           └── app.js           # Frontend logic
│   │
│   └── 🔬 dspy_pipeline/            # DSPy Optimization Pipeline
│       ├── __init__.py
│       ├── main.py                  # Main pipeline script
│       ├── enhanced_feedback_optimization.py
│       ├── feedback_optimization_test.py
│       ├── feedback_with_optimization.py
│       ├── style_aware_verification.py
│       ├── visual_verification_system.py
│       ├── models/
│       │   ├── __init__.py
│       │   └── style_extraction.py  # Pydantic models
│       ├── utils/
│       │   ├── __init__.py
│       │   ├── data_utils.py        # Data handling utilities
│       │   ├── oauth_client.py      # OAuth client
│       │   └── simple_cot.py        # Simple CoT utilities
│       └── evaluation/
│           ├── __init__.py
│           └── evaluation.py        # Evaluation logic
│
├── 🧪 tests/                        # Test Suites
│   ├── __init__.py
│   ├── unit/                        # Unit Tests
│   │   ├── __init__.py
│   │   ├── simple_cot_test.py
│   │   ├── simple_feedback_test.py
│   │   ├── test_async.py
│   │   └── test_cot_ideas.py
│   ├── integration/                 # Integration Tests
│   │   ├── __init__.py
│   │   ├── final_integration_test.py
│   │   ├── run_feedback_test.py
│   │   ├── run_style_test.py
│   │   ├── test_enhanced_10_examples.py
│   │   └── test_miprov2_10_examples.py
│   └── web/                         # Web Application Tests
│       ├── __init__.py
│       └── test_web_api.py
│
├── 🛠️ scripts/                      # Utility Scripts
│   ├── setup/                       # Setup & Deployment
│   │   ├── setup_and_run.py         # Main setup script
│   │   └── run_app.py               # App runner
│   └── data_processing/             # Data Processing
│       ├── create_tile_examples.py  # Create training examples
│       └── download_and_split_images.py # Image processing
│
├── 📊 data/                         # Data Files
│   ├── examples/                    # Training examples
│   │   ├── style_examples.jsonl
│   │   └── style_images_list.json
│   └── style_images/                # Style image data
│       └── groovjones_styles/       # Groovjones style collection
│
├── 📈 results/                      # Generated Results
│   ├── cot_results/                 # CoT generation results
│   ├── enhanced_feedback_optimization/
│   ├── enhanced_test_10_examples/
│   ├── feedback_optimization/
│   ├── feedback_test_simple/
│   ├── miprov2_test_10_examples/
│   ├── style_optimization/
│   ├── saved_predictors/            # Saved DSPy predictors
│   ├── out.json
│   └── out.txt
│
├── 📚 docs/                         # Documentation
│   ├── README.md                    # Original README
│   ├── DEPLOYMENT_GUIDE.md          # Deployment instructions
│   ├── WEB_FRONTEND_README.md       # Web frontend guide
│   ├── ASYNC_IMPLEMENTATION.md      # Async implementation details
│   ├── FEEDBACK_OPTIMIZATION_README.md # Feedback optimization guide
│   └── grader_prompt.md             # Grading prompt template
│
├── 🎭 demos/                        # Demo Files
│   ├── debug_app.py                 # Debug Flask app
│   ├── frontend_demo.html           # Standalone frontend demo
│   ├── minimal_flask.py             # Minimal Flask example
│   ├── simple_web_demo.html         # Static demo
│   └── test_flask.py                # Flask testing
│
├── 📋 Configuration Files
│   ├── README.md                    # Main README
│   ├── IMPORT_GUIDE.md              # Import path guide
│   ├── REPOSITORY_STRUCTURE.md      # This file
│   ├── pyproject.toml               # Project configuration
│   └── uv.lock                      # Dependency lock file
│
└── 📦 archive/                      # Archived files (if any)
```

## 🎯 Key Components

### 🧠 Chain of Thought System (`src/cot_system/`)
- **Core Implementation**: Complete DSPy-based CoT reasoning system
- **Template Support**: 8 specialized templates for different domains
- **Structured Output**: Thinking chain + bullet points + evidence

### 🌐 Web Application (`src/web/`)
- **Flask Backend**: RESTful API with CoT integration
- **Modern Frontend**: Responsive HTML/CSS/JS interface
- **Interactive Features**: Real-time generation, template selection, save functionality

### 🔬 DSPy Pipeline (`src/dspy_pipeline/`)
- **Optimization Methods**: LabeledFewShot, BootstrapFewShot, MIPROv2, etc.
- **Feedback Systems**: Image generation and feedback optimization
- **Style Analysis**: Advanced style extraction and verification
- **Async Support**: High-performance async operations

## 🚀 Quick Start Commands

### Run Web Application
```bash
python scripts/setup/setup_and_run.py
# or
python -m src.web.app
```

### Run DSPy Pipeline
```bash
python -m src.dspy_pipeline.main
```

### Run Tests
```bash
# Unit tests
python -m pytest tests/unit/

# Integration tests  
python -m pytest tests/integration/

# Web tests
python tests/web/test_web_api.py
```

### Generate Ideas (CoT)
```bash
python -m src.cot_system.cot_idea_generator
```

## 📖 Documentation

- **[Main README](README.md)**: Project overview and quick start
- **[Deployment Guide](docs/DEPLOYMENT_GUIDE.md)**: Complete deployment instructions
- **[Web Frontend Guide](docs/WEB_FRONTEND_README.md)**: Web application details
- **[Import Guide](IMPORT_GUIDE.md)**: Updated import paths after reorganization

## 🧹 Organization Benefits

### ✅ **Clear Separation of Concerns**
- CoT system isolated in its own module
- Web application cleanly separated
- DSPy pipeline components organized by function

### ✅ **Improved Maintainability**
- Logical directory structure
- Consistent naming conventions
- Clear dependency relationships

### ✅ **Better Testing**
- Tests organized by type (unit, integration, web)
- Easy to run specific test suites
- Clear test coverage areas

### ✅ **Enhanced Development Experience**
- Easy to find relevant files
- Clear project structure
- Proper Python package structure with `__init__.py` files

### ✅ **Production Ready**
- Clean deployment structure
- Organized documentation
- Proper configuration management

## 🔄 Migration Notes

If you have existing code that imports from the old structure, see **[IMPORT_GUIDE.md](IMPORT_GUIDE.md)** for updated import paths.

The repository is now organized, clean, and ready for professional development! 🎉
