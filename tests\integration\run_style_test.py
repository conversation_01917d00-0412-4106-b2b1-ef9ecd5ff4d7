"""
Automated runner for style-aware verification system.
"""

import asyncio
from style_aware_verification import StyleAwareVerificationSystem


async def run_optimization_test():
    """Run the style-aware optimization test with 2 styles."""
    print("🚀 Running automated style-aware optimization test")
    print("Testing with 2 styles (8 images total)")
    
    system = StyleAwareVerificationSystem()
    results = await system.run_style_aware_optimization(num_styles=2)
    
    if results:
        print("\n✅ Test completed successfully!")
        print(f"📊 Overall improvement: {results['overall_statistics']['average_improvement']:+.3f}")
        print(f"🎯 Success rate: {results['overall_statistics']['improvement_rate']*100:.1f}%")
    else:
        print("❌ Test failed")


async def run_verification_test():
    """Run the style-aware verification test with 2 styles."""
    print("🚀 Running automated style-aware verification test")
    print("Testing consistency across tiles for 2 styles")
    
    system = StyleAwareVerificationSystem()
    results = await system.run_style_aware_verification(num_styles=2)
    
    if results:
        print("\n✅ Verification completed successfully!")
        stats = results['overall_statistics']
        print(f"📊 Average similarity: {stats['average_similarity']:.3f}")
        print(f"🎯 Consistency distribution: {stats['consistency_distribution']}")
    else:
        print("❌ Verification failed")


if __name__ == "__main__":
    print("Choose test type:")
    print("1. Verification (consistency across tiles)")
    print("2. Optimization (DSPy optimization with tiles)")
    
    choice = input("Enter choice (1-2): ").strip()
    
    if choice == "1":
        asyncio.run(run_verification_test())
    elif choice == "2":
        asyncio.run(run_optimization_test())
    else:
        print("❌ Invalid choice")
