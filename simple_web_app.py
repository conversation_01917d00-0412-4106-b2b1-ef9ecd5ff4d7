"""
Simple, streamlined version of the Chain of Thought web application.
This version handles all the path issues and should work reliably.
"""

import sys
import os
from pathlib import Path
from flask import Flask, render_template, request, jsonify
from flask_cors import CORS

# Setup paths
repo_root = Path(__file__).parent
src_path = repo_root / 'src'
web_path = src_path / 'web'

# Add src to Python path
if str(src_path) not in sys.path:
    sys.path.insert(0, str(src_path))

# Import CoT system and validation
from cot_system.cot_idea_generator import CoTIdeaSystem
from validation.google_search_validator import GoogleSearchValidator

# Configure Flask with correct paths
app = Flask(__name__, 
           template_folder=str(web_path / 'templates'),
           static_folder=str(web_path / 'static'))
CORS(app)

# Global systems
cot_system = None
validator = None

def initialize_cot_system():
    """Initialize the CoT system and validator."""
    global cot_system, validator
    try:
        print("🔄 Initializing CoT system...")
        cot_system = CoTIdeaSystem()
        if cot_system.generator:
            print("✅ CoT system initialized successfully")

            print("🔄 Initializing ground truth validator...")
            validator = GoogleSearchValidator(max_results=5)
            print("✅ Validator initialized successfully")

            return True
        else:
            print("❌ Failed to initialize CoT system")
            return False
    except Exception as e:
        print(f"❌ Error initializing systems: {e}")
        return False

@app.route('/')
def index():
    """Serve the main page."""
    return render_template('index.html')

@app.route('/api/templates')
def get_templates():
    """Get available templates."""
    if not cot_system:
        return jsonify({"error": "System not initialized"}), 500
    
    templates = []
    for template_type, description in cot_system.templates.items():
        templates.append({
            "id": template_type,
            "name": template_type.replace('_', ' ').title(),
            "description": description[:100] + "..." if len(description) > 100 else description
        })
    
    return jsonify({"templates": templates})

@app.route('/api/generate', methods=['POST'])
def generate_ideas():
    """Generate ideas based on user input."""
    if not cot_system:
        return jsonify({"error": "System not initialized"}), 500
    
    try:
        data = request.get_json()
        topic = data.get('topic', '').strip()
        template_id = data.get('template', '')
        custom_context = data.get('context', '').strip()
        
        if not topic:
            return jsonify({"error": "Topic is required"}), 400
        
        # Get context from template or use custom
        context = ""
        if template_id and template_id in cot_system.templates:
            context = cot_system.get_template_context(template_id)
        elif custom_context:
            context = custom_context
        
        # Generate ideas
        ideas = cot_system.generate_ideas(topic=topic, context=context)
        
        # Convert to dict for JSON response
        response_data = {
            "topic": ideas.topic,
            "thinking_chain": [
                {
                    "step_number": step.step_number,
                    "thought": step.thought,
                    "category": step.category,
                    "confidence": step.confidence
                }
                for step in ideas.thinking_chain
            ],
            "bullet_points": [
                {
                    "content": bp.content,
                    "priority": bp.priority,
                    "category": bp.category,
                    "supporting_evidence": bp.supporting_evidence,
                    "related_concepts": bp.related_concepts
                }
                for bp in ideas.bullet_points
            ],
            "summary": ideas.summary,
            "next_steps": ideas.next_steps
        }
        
        return jsonify(response_data)
        
    except Exception as e:
        print(f"Error generating ideas: {e}")
        return jsonify({"error": f"Failed to generate ideas: {str(e)}"}), 500

@app.route('/api/validate', methods=['POST'])
def validate_ideas():
    """Validate ideas against ground truth from Google search."""
    if not cot_system or not validator:
        return jsonify({"error": "Systems not initialized"}), 500

    try:
        data = request.get_json()
        topic = data.get('topic', '').strip()
        context = data.get('context', '').strip()
        num_results = data.get('num_results', 5)

        if not topic:
            return jsonify({"error": "Topic is required"}), 400

        # Run validation
        ideas, ground_truth, validation_score = validator.run_validation(
            topic=topic,
            context=context,
            num_results=num_results
        )

        # Convert to dict for JSON response
        response_data = {
            "ideas": {
                "topic": ideas.topic,
                "thinking_chain": [
                    {
                        "step_number": step.step_number,
                        "thought": step.thought,
                        "category": step.category,
                        "confidence": step.confidence
                    }
                    for step in ideas.thinking_chain
                ],
                "bullet_points": [
                    {
                        "content": bp.content,
                        "priority": bp.priority,
                        "category": bp.category,
                        "supporting_evidence": bp.supporting_evidence,
                        "related_concepts": bp.related_concepts
                    }
                    for bp in ideas.bullet_points
                ],
                "summary": ideas.summary,
                "next_steps": ideas.next_steps
            },
            "ground_truth": {
                "query": ground_truth.query,
                "key_concepts": ground_truth.key_concepts,
                "common_themes": ground_truth.common_themes,
                "expert_recommendations": ground_truth.expert_recommendations,
                "num_search_results": len(ground_truth.search_results)
            },
            "validation_score": {
                "overall_score": validation_score.overall_score,
                "concept_overlap": validation_score.concept_overlap,
                "theme_alignment": validation_score.theme_alignment,
                "recommendation_quality": validation_score.recommendation_quality,
                "novelty_score": validation_score.novelty_score,
                "detailed_feedback": validation_score.detailed_feedback
            }
        }

        return jsonify(response_data)

    except Exception as e:
        print(f"Error in validation: {e}")
        return jsonify({"error": f"Validation failed: {str(e)}"}), 500

@app.route('/health')
def health_check():
    """Health check endpoint."""
    cot_status = "initialized" if cot_system and cot_system.generator else "not initialized"
    validator_status = "initialized" if validator else "not initialized"
    return jsonify({
        "status": "healthy",
        "cot_system": cot_status,
        "validator": validator_status
    })

def main():
    """Main function to run the web application."""
    print("🧠 Chain of Thought Idea Generator - Simple Web App")
    print("=" * 55)
    
    # Check if required files exist
    template_file = web_path / 'templates' / 'index.html'
    if not template_file.exists():
        print(f"❌ Template file not found: {template_file}")
        print("💡 Make sure you're running from the repository root")
        return False
    
    print(f"✅ Template file found: {template_file}")
    
    # Initialize CoT system
    if not initialize_cot_system():
        print("❌ Failed to initialize CoT system")
        return False
    
    print("🚀 Starting Flask server...")
    print("🌐 Server will be available at: http://localhost:5000")
    print("📝 Open your browser and navigate to the URL above")
    print("🛑 Press Ctrl+C to stop the server")
    print("-" * 55)
    
    try:
        app.run(debug=True, host='127.0.0.1', port=5000, use_reloader=False)
        return True
    except KeyboardInterrupt:
        print("\n👋 Server stopped by user")
        return True
    except Exception as e:
        print(f"❌ Server error: {e}")
        return False

if __name__ == '__main__':
    success = main()
    if not success:
        print("\n❌ Failed to start web application")
        sys.exit(1)
