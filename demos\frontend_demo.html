<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🧠 Chain of Thought Idea Generator</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        header {
            text-align: center;
            margin-bottom: 40px;
            color: white;
        }

        header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }

        .main-card {
            background: white;
            padding: 40px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }

        .form-section {
            margin-bottom: 30px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s ease;
        }

        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .form-group textarea {
            resize: vertical;
            min-height: 100px;
        }

        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
            display: inline-flex;
            align-items: center;
            gap: 10px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        .btn:active {
            transform: translateY(0);
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .loading {
            display: none;
            text-align: center;
            padding: 40px;
            color: #667eea;
        }

        .loading.show {
            display: block;
        }

        .spinner {
            border: 3px solid #f3f3f3;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .demo-section {
            background: #f8f9fa;
            padding: 30px;
            border-radius: 10px;
            margin-top: 30px;
        }

        .demo-section h3 {
            color: #667eea;
            margin-bottom: 20px;
            font-size: 1.5rem;
        }

        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }

        .feature-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .feature-card i {
            font-size: 2rem;
            color: #667eea;
            margin-bottom: 15px;
        }

        .feature-card h4 {
            margin-bottom: 10px;
            color: #333;
        }

        .feature-card p {
            color: #666;
            font-size: 0.9rem;
        }

        .status-message {
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
            display: none;
        }

        .status-message.success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }

        .status-message.error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }

        .status-message.show {
            display: block;
        }

        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }
            
            header h1 {
                font-size: 2rem;
            }
            
            .main-card {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <h1><i class="fas fa-brain"></i> Chain of Thought Idea Generator</h1>
            <p>Generate structured ideas with step-by-step AI reasoning</p>
        </header>

        <main>
            <div class="main-card">
                <div class="form-section">
                    <h2><i class="fas fa-lightbulb"></i> Generate Ideas</h2>
                    
                    <form id="ideaForm">
                        <div class="form-group">
                            <label for="topic">
                                <i class="fas fa-target"></i> Topic or Question
                            </label>
                            <input 
                                type="text" 
                                id="topic" 
                                name="topic" 
                                placeholder="e.g., improving customer service, building a mobile app, solving climate change..."
                                required
                            >
                        </div>

                        <div class="form-group">
                            <label for="template">
                                <i class="fas fa-template"></i> Template (Optional)
                            </label>
                            <select id="template" name="template">
                                <option value="">Choose a template...</option>
                                <option value="business">🏢 Business - Market opportunities, revenue models</option>
                                <option value="technical">⚙️ Technical - Architecture, scalability, security</option>
                                <option value="creative">🎨 Creative - Innovation, aesthetics, user experience</option>
                                <option value="research">🔬 Research - Methodology, data sources, analysis</option>
                                <option value="problem-solving">🔧 Problem-solving - Root cause analysis, solutions</option>
                                <option value="strategic">📈 Strategic - Long-term vision, stakeholder impact</option>
                                <option value="educational">📚 Educational - Learning objectives, pedagogy</option>
                                <option value="process">🔄 Process Improvement - Workflow optimization</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="context">
                                <i class="fas fa-info-circle"></i> Additional Context (Optional)
                            </label>
                            <textarea 
                                id="context" 
                                name="context" 
                                placeholder="Provide any specific constraints, requirements, or additional information..."
                            ></textarea>
                        </div>

                        <button type="submit" class="btn" id="generateBtn">
                            <i class="fas fa-magic"></i>
                            Generate Ideas
                        </button>
                    </form>
                </div>

                <div class="loading" id="loading">
                    <div class="spinner"></div>
                    <h3>🧠 AI is thinking step by step...</h3>
                    <p>Analyzing your topic and generating structured ideas</p>
                </div>

                <div class="status-message" id="statusMessage"></div>

                <div class="demo-section">
                    <h3><i class="fas fa-star"></i> Features</h3>
                    
                    <div class="feature-grid">
                        <div class="feature-card">
                            <i class="fas fa-brain"></i>
                            <h4>Chain of Thought</h4>
                            <p>See the AI's step-by-step reasoning process with confidence scores</p>
                        </div>
                        
                        <div class="feature-card">
                            <i class="fas fa-list-ul"></i>
                            <h4>Structured Ideas</h4>
                            <p>Ideas organized by priority, category, with supporting evidence</p>
                        </div>
                        
                        <div class="feature-card">
                            <i class="fas fa-template"></i>
                            <h4>Smart Templates</h4>
                            <p>8 specialized contexts for domain-specific idea generation</p>
                        </div>
                        
                        <div class="feature-card">
                            <i class="fas fa-save"></i>
                            <h4>Save Results</h4>
                            <p>Export your ideas and reasoning to JSON files</p>
                        </div>
                        
                        <div class="feature-card">
                            <i class="fas fa-mobile-alt"></i>
                            <h4>Responsive Design</h4>
                            <p>Works perfectly on desktop, tablet, and mobile devices</p>
                        </div>
                        
                        <div class="feature-card">
                            <i class="fas fa-rocket"></i>
                            <h4>Next Steps</h4>
                            <p>Get actionable implementation steps for your ideas</p>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script>
        // Demo functionality for the frontend
        document.getElementById('ideaForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const topic = document.getElementById('topic').value.trim();
            if (!topic) {
                showMessage('Please enter a topic or question.', 'error');
                return;
            }
            
            // Show loading state
            document.getElementById('loading').classList.add('show');
            document.getElementById('generateBtn').disabled = true;
            
            // Simulate API call (in real app, this would call the Flask backend)
            setTimeout(() => {
                document.getElementById('loading').classList.remove('show');
                document.getElementById('generateBtn').disabled = false;
                
                showMessage(`Demo: Ideas would be generated for "${topic}". Connect to Flask backend for full functionality!`, 'success');
            }, 3000);
        });
        
        function showMessage(text, type) {
            const messageEl = document.getElementById('statusMessage');
            messageEl.textContent = text;
            messageEl.className = `status-message ${type} show`;
            
            setTimeout(() => {
                messageEl.classList.remove('show');
            }, 5000);
        }
    </script>
</body>
</html>
