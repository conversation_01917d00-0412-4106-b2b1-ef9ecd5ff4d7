"""
Enhanced feedback optimization with 10-20 examples for better results.
Includes progress tracking, cost estimation, and improved optimization strategies.
"""

import asyncio
import json
import os
from datetime import datetime
import dspy
from dspy import Example

# Import our modules
from main import setup_dspy, create_predictor, optimize_predictor_labeled_fewshot
from utils.data_utils import load_examples
from simple_feedback_test import generate_image_from_style, analyze_generated_image, calculate_style_similarity


class EnhancedFeedbackOptimizer:
    """Enhanced feedback optimizer with better progress tracking and more examples."""
    
    def __init__(self):
        self.original_predictor = None
        self.optimized_predictor = None
        self.feedback_data = []
        self.cost_per_image = 0.04  # DALL-E 3 cost
    
    def setup(self):
        """Setup DSPy and create original predictor."""
        setup_dspy()
        lm = dspy.LM("openai/gpt-4o-mini", cache_in_memory=False)
        dspy.configure(lm=lm)
        self.original_predictor = create_predictor()
        print("✅ DSPy setup complete")
    
    def estimate_cost(self, num_examples: int) -> float:
        """Estimate the cost of running the optimization."""
        return num_examples * self.cost_per_image
    
    def get_diverse_examples(self, examples: list, num_examples: int) -> list:
        """Select diverse examples from different styles to avoid overfitting."""
        # Group examples by style name
        style_groups = {}
        for example in examples:
            try:
                metadata = json.loads(example["metadata"])
                style_name = metadata.get("style_name", "unknown")
                if style_name not in style_groups:
                    style_groups[style_name] = []
                style_groups[style_name].append(example)
            except:
                continue
        
        # Select examples from different styles
        selected = []
        style_names = list(style_groups.keys())
        
        for i in range(num_examples):
            style_idx = i % len(style_names)
            style_name = style_names[style_idx]
            
            if style_groups[style_name]:
                # Take the first available example from this style
                example_idx = i // len(style_names)
                if example_idx < len(style_groups[style_name]):
                    selected.append(style_groups[style_name][example_idx])
                else:
                    # If we've exhausted this style, take from any remaining
                    remaining = [ex for group in style_groups.values() for ex in group if ex not in selected]
                    if remaining:
                        selected.append(remaining[0])
        
        print(f"📊 Selected {len(selected)} examples from {len(set(json.loads(ex['metadata'])['style_name'] for ex in selected))} different styles")
        return selected[:num_examples]
    
    async def collect_feedback_data_with_progress(self, num_examples: int = 10) -> list:
        """Collect feedback data with progress tracking and diverse style selection."""
        print(f"\n📊 Collecting feedback data from {num_examples} examples...")
        estimated_cost = self.estimate_cost(num_examples)
        print(f"💰 Estimated cost: ${estimated_cost:.2f}")
        
        # Load and select diverse examples
        examples_file = "examples/groovjones_styles/tile_examples.jsonl"
        all_examples = load_examples(examples_file)
        test_examples = self.get_diverse_examples(all_examples, num_examples)
        
        # Create output directory
        output_dir = "enhanced_feedback_optimization"
        os.makedirs(output_dir, exist_ok=True)
        
        feedback_results = []
        successful_count = 0
        failed_count = 0
        
        print(f"\n🚀 Processing {len(test_examples)} examples...")
        
        for i, example in enumerate(test_examples, 1):
            try:
                print(f"\n{'='*10} Example {i}/{len(test_examples)} {'='*10}")
                
                # Extract data
                original_instruction = example["instruction"]
                expected_style = example["style"]
                expected_metadata = example["metadata"]
                
                # Get style name
                metadata = json.loads(expected_metadata)
                style_name = metadata.get("style_name", f"style_{i}").replace(" ", "_")
                
                print(f"🎨 Style: {style_name}")
                print(f"📈 Progress: {i}/{len(test_examples)} ({i/len(test_examples)*100:.1f}%)")
                
                # Generate image from expected style
                timestamp = datetime.now().strftime("%H%M%S")
                image_path = os.path.join(output_dir, f"feedback_{style_name}_{i:02d}_{timestamp}.png")
                
                await generate_image_from_style(expected_style, image_path)
                
                # Analyze generated image with original predictor
                print(f"🔍 Analyzing generated image...")
                actual_analysis = await analyze_generated_image(self.original_predictor, image_path)
                
                # Calculate similarity
                similarity = calculate_style_similarity(expected_style, actual_analysis["style"])
                
                print(f"📊 Similarity: {similarity['overall_score']:.3f}")
                
                # Store feedback data
                feedback_item = {
                    "instruction": original_instruction,
                    "expected_style": expected_style,
                    "actual_style": actual_analysis["style"],
                    "similarity_score": similarity["overall_score"],
                    "style_name": style_name,
                    "image_path": image_path,
                    "index": i
                }
                
                feedback_results.append(feedback_item)
                self.feedback_data.append(feedback_item)
                successful_count += 1
                
                # Show running average
                if successful_count > 0:
                    avg_similarity = sum(item["similarity_score"] for item in feedback_results) / len(feedback_results)
                    print(f"📈 Running average similarity: {avg_similarity:.3f}")
                
            except Exception as e:
                print(f"❌ Error processing example {i}: {e}")
                failed_count += 1
                continue
        
        print(f"\n📊 Collection Summary:")
        print(f"   ✅ Successful: {successful_count}")
        print(f"   ❌ Failed: {failed_count}")
        
        if feedback_results:
            avg_similarity = sum(item["similarity_score"] for item in feedback_results) / len(feedback_results)
            print(f"   📈 Average similarity before optimization: {avg_similarity:.3f}")
            
            # Show distribution
            scores = [item["similarity_score"] for item in feedback_results]
            print(f"   📊 Score range: {min(scores):.3f} - {max(scores):.3f}")
        
        return feedback_results
    
    def create_optimization_examples(self) -> list:
        """Create DSPy training examples from feedback data."""
        print(f"\n🎯 Creating optimization examples from {len(self.feedback_data)} feedback items...")
        
        optimization_examples = []
        
        for feedback in self.feedback_data:
            # Create example that teaches the model the expected output
            example = Example(
                instruction=feedback["instruction"],
                style=feedback["expected_style"]  # This is what we want the model to produce
            ).with_inputs('instruction')
            
            optimization_examples.append(example)
        
        print(f"   Created {len(optimization_examples)} optimization examples")
        return optimization_examples
    
    async def optimize_predictor_enhanced(self, optimization_examples: list):
        """Enhanced predictor optimization with better parameters."""
        print(f"\n⚡ Optimizing predictor with {len(optimization_examples)} examples...")
        
        try:
            # Use more examples in the optimization
            k = min(len(optimization_examples), 16)  # Use up to 16 examples
            print(f"   Using k={k} examples for LabeledFewShot optimization")
            
            self.optimized_predictor = optimize_predictor_labeled_fewshot(
                self.original_predictor,
                optimization_examples,
                k=k
            )
            print("✅ Predictor optimization complete")
            
        except Exception as e:
            print(f"❌ Error optimizing predictor: {e}")
            self.optimized_predictor = self.original_predictor  # Fallback
    
    async def test_optimized_predictor_detailed(self) -> dict:
        """Test the optimized predictor with detailed analysis."""
        print(f"\n🧪 Testing optimized predictor on {len(self.feedback_data)} examples...")
        
        if not self.optimized_predictor:
            print("❌ No optimized predictor available")
            return {}
        
        test_results = []
        improvements = []
        
        for i, feedback in enumerate(self.feedback_data, 1):
            try:
                print(f"   Testing {i}/{len(self.feedback_data)}: {feedback['style_name']}")
                
                # Analyze the same generated image with optimized predictor
                optimized_analysis = await analyze_generated_image(
                    self.optimized_predictor, 
                    feedback["image_path"]
                )
                
                # Calculate new similarity
                new_similarity = calculate_style_similarity(
                    feedback["expected_style"], 
                    optimized_analysis["style"]
                )
                
                improvement = new_similarity["overall_score"] - feedback["similarity_score"]
                improvements.append(improvement)
                
                test_result = {
                    "style_name": feedback["style_name"],
                    "original_similarity": feedback["similarity_score"],
                    "optimized_similarity": new_similarity["overall_score"],
                    "improvement": improvement,
                    "index": feedback["index"]
                }
                
                test_results.append(test_result)
                
                # Show progress
                if i % 5 == 0 or i == len(self.feedback_data):
                    avg_improvement = sum(improvements) / len(improvements)
                    print(f"      Progress: {i}/{len(self.feedback_data)}, Avg improvement: {avg_improvement:+.3f}")
                
            except Exception as e:
                print(f"   ❌ Error testing {feedback['style_name']}: {e}")
                continue
        
        # Calculate comprehensive statistics
        if test_results:
            original_scores = [r["original_similarity"] for r in test_results]
            optimized_scores = [r["optimized_similarity"] for r in test_results]
            improvements = [r["improvement"] for r in test_results]
            
            summary = {
                "test_results": test_results,
                "statistics": {
                    "num_tests": len(test_results),
                    "average_original": sum(original_scores) / len(original_scores),
                    "average_optimized": sum(optimized_scores) / len(optimized_scores),
                    "average_improvement": sum(improvements) / len(improvements),
                    "best_improvement": max(improvements),
                    "worst_improvement": min(improvements),
                    "improved_count": len([i for i in improvements if i > 0]),
                    "degraded_count": len([i for i in improvements if i < 0]),
                    "unchanged_count": len([i for i in improvements if abs(i) < 0.001])
                }
            }
            
            stats = summary["statistics"]
            print(f"\n📊 Detailed Optimization Results:")
            print(f"   📈 Average similarity before: {stats['average_original']:.3f}")
            print(f"   📈 Average similarity after:  {stats['average_optimized']:.3f}")
            print(f"   📈 Average improvement:       {stats['average_improvement']:+.3f}")
            print(f"   🎯 Best improvement:          {stats['best_improvement']:+.3f}")
            print(f"   📉 Worst change:              {stats['worst_improvement']:+.3f}")
            print(f"   ✅ Improved examples:         {stats['improved_count']}/{stats['num_tests']}")
            print(f"   ❌ Degraded examples:         {stats['degraded_count']}/{stats['num_tests']}")
            
            return summary
        
        return {}
    
    async def run_enhanced_pipeline(self, num_examples: int = 10):
        """Run the enhanced feedback optimization pipeline."""
        print("🚀 Enhanced Feedback Optimization Pipeline")
        print("=" * 60)
        
        # Cost confirmation
        estimated_cost = self.estimate_cost(num_examples)
        print(f"💰 Estimated cost: ${estimated_cost:.2f} for {num_examples} images")
        
        confirm = input(f"Continue with {num_examples} examples? (y/n): ").strip().lower()
        if confirm != 'y':
            print("❌ Cancelled by user")
            return
        
        try:
            # Step 1: Setup
            self.setup()
            
            # Step 2: Collect feedback data with progress tracking
            feedback_data = await self.collect_feedback_data_with_progress(num_examples)
            
            if not feedback_data:
                print("❌ No feedback data collected")
                return
            
            # Step 3: Create optimization examples
            opt_examples = self.create_optimization_examples()
            
            # Step 4: Enhanced optimization
            await self.optimize_predictor_enhanced(opt_examples)
            
            # Step 5: Detailed testing
            test_summary = await self.test_optimized_predictor_detailed()
            
            # Step 6: Save comprehensive results
            output_dir = "enhanced_feedback_optimization"
            results_file = os.path.join(output_dir, f"enhanced_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json")
            
            results = {
                "configuration": {
                    "num_examples": num_examples,
                    "estimated_cost": estimated_cost,
                    "optimization_method": "LabeledFewShot"
                },
                "feedback_data": feedback_data,
                "test_summary": test_summary,
                "timestamp": datetime.now().isoformat()
            }
            
            with open(results_file, 'w') as f:
                json.dump(results, f, indent=2)
            
            print(f"\n📁 Complete results saved to: {results_file}")
            print("✅ Enhanced pipeline completed successfully!")
            
            return results
            
        except Exception as e:
            print(f"❌ Pipeline error: {e}")
            import traceback
            traceback.print_exc()


async def main():
    """Main function with options for different numbers of examples."""
    print("🎨 Enhanced DSPy Feedback Optimization")
    print("Choose the number of examples to use:")
    print("1. 10 examples (~$0.40)")
    print("2. 15 examples (~$0.60)")
    print("3. 20 examples (~$0.80)")
    print("4. Custom number")
    
    choice = input("Enter choice (1-4): ").strip()
    
    if choice == "1":
        num_examples = 10
    elif choice == "2":
        num_examples = 15
    elif choice == "3":
        num_examples = 20
    elif choice == "4":
        try:
            num_examples = int(input("Enter number of examples: ").strip())
            if num_examples < 1 or num_examples > 50:
                print("❌ Please enter a number between 1 and 50")
                return
        except ValueError:
            print("❌ Invalid number")
            return
    else:
        print("❌ Invalid choice")
        return
    
    optimizer = EnhancedFeedbackOptimizer()
    await optimizer.run_enhanced_pipeline(num_examples)


if __name__ == "__main__":
    asyncio.run(main())
