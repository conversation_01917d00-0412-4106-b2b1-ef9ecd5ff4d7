import dspy
import httpx
import time
import os
from abc import ABC, abstractmethod
from typing import Any, Dict, List

# Clear proxy environment variables to ensure direct connection
proxy_vars = ['HTTP_PROXY', 'HTTPS_PROXY', 'http_proxy', 'https_proxy', 'ALL_PROXY', 'all_proxy']
for var in proxy_vars:
    if var in os.environ:
        print(f"Cleared {var} environment variable (was: {os.environ[var]})")
        del os.environ[var]
    # Also set to empty string as extra safety
    os.environ[var] = ""

class DNABot(dspy.LM):
    """
    A DSPy Language Model that directly calls your OAuth2-protected API
    without going through LiteLLM's provider routing system.
    """
    def __init__(
        self,
        model: str,
        token_url: str,
        api_base: str,
        client_id: str = None,
        client_secret: str = None,
        verify_ssl: bool = False,  # Default to False for corporate environments
        use_proxy: bool = False,   # Default to False to bypass corporate proxies
        **kwargs,
    ):
        super().__init__(model)
        self.model = model
        self.token_url = token_url
        self.api_base = api_base
        
        # Get credentials from arguments or environment variables
        self.client_id = client_id or os.environ.get("DNABOT_OAUTH_CLIENT_ID")
        self.client_secret = client_secret or os.environ.get("DNABOT_OAUTH_CLIENT_SECRET")
        
        if not self.client_id or not self.client_secret:
            raise ValueError(
                "Client ID and Client Secret must be provided either as arguments "
                "or as DNABOT_OAUTH_CLIENT_ID and DNABOT_OAUTH_CLIENT_SECRET environment variables."
            )

        # Create httpx client with configurable SSL verification
        if use_proxy:
            # Use default proxy settings (respects environment variables)
            self.client = httpx.Client(verify=verify_ssl, timeout=300.0)
        else:
            # Disable proxy usage by not passing any proxy configuration
            # and relying on cleared environment variables
            self.client = httpx.Client(verify=verify_ssl, timeout=300.0)
        self.access_token = None
        self.token_expiry = 0
        self.kwargs = kwargs
        
        # Initialize history for DSPy compatibility
        self.history = []
        
        print(f"Initialized DirectOAuthLM for model {self.model}.")
        if not verify_ssl:
            print("Warning: SSL certificate verification is disabled.")
        if not use_proxy:
            print("Info: Proxy usage is disabled - direct connections only.")

    def _get_access_token(self):
        """Fetches a new access token if needed."""
        if self.access_token and time.time() < self.token_expiry - 60:
            return

        print("Fetching new OAuth2 access token...")
        try:
            response = self.client.post(
                self.token_url,
                data={
                    "grant_type": "client_credentials",
                    "client_id": self.client_id,
                    "client_secret": self.client_secret,
                },
            )
            response.raise_for_status()
            token_data = response.json()
            
            if "access_token" not in token_data:
                raise ValueError("OAuth token response did not contain 'access_token'")

            self.access_token = token_data["access_token"]
            self.token_expiry = time.time() + token_data.get("expires_in", 3600)
            self.client.headers["Authorization"] = f"Bearer {self.access_token}"
            print("Successfully obtained new access token.")
            
        except httpx.HTTPStatusError as e:
            print(f"Error fetching access token: {e.response.status_code} - {e.response.text}")
            raise
        except Exception as e:
            print(f"An unexpected error occurred while fetching token: {e}")
            raise

    def basic_request(self, prompt: str, **kwargs) -> Dict[str, Any]:
        """
        Makes a direct request to your API endpoint.
        """
        self._get_access_token()
        
        # Combine default and request-specific kwargs
        request_kwargs = {**self.kwargs, **kwargs}
        
        # Create payload matching your curl example
        payload = {
            "model": self.model,  # Use the raw model name
            "messages": [{"role": "user", "content": prompt}],
            "max_tokens": request_kwargs.get("max_tokens", 300),
            "temperature": request_kwargs.get("temperature", 1.2),
            "top_p": request_kwargs.get("top_p", 1),
            "frequency_penalty": request_kwargs.get("frequency_penalty", 0),
            "n": request_kwargs.get("n", 1),
            "echo": request_kwargs.get("echo", False),
            "stop": request_kwargs.get("stop", "#stop"),
        }
        
        # Add user field if provided
        if "user" in request_kwargs:
            payload["user"] = request_kwargs["user"]
        
        # Add response_format if provided
        if "response_format" in request_kwargs:
            payload["response_format"] = request_kwargs["response_format"]
        
        completions = []
        try:
            response = self.client.post(self.api_base, json=payload, timeout=300.0)
            response.raise_for_status()
            
            # Extract text from OpenAI-compatible response
            data = response.json()
            completions = [choice["message"]["content"] for choice in data.get("choices", [])]

        except httpx.HTTPStatusError as e:
            print(f"Error during API call: {e.response.status_code} - {e.response.text}")
        except httpx.ReadTimeout:
            print("Error: The request timed out.")
        except Exception as e:
            print(f"Unexpected error during API call: {e}")
        
        return {"choices": [{"text": text} for text in completions]}

    def __call__(self, prompt=None, messages=None, **kwargs):
        """
        Override the __call__ method to bypass LiteLLM completely.
        This is what DSPy's Predict class will call.
        """
        if messages:
            # If messages are provided, use the last user message as prompt
            prompt = messages[-1]["content"] if messages else ""
        
        if not prompt:
            return {"choices": [{"text": ""}]}
            
        result = self.basic_request(prompt, **kwargs)
        
        # Store in history for DSPy compatibility
        self.history.append({
            "prompt": prompt,
            "response": result,
            "kwargs": kwargs
        })
        
        return result

    def generate(self, prompt, n=1, **kwargs):
        """
        Alternative method for generating completions.
        """
        kwargs["n"] = n
        return self.__call__(prompt, **kwargs)

    def chat(self, messages, **kwargs):
        """
        Chat interface for multi-turn conversations.
        """
        return self.__call__(messages=messages, **kwargs)


# Usage example
if __name__ == '__main__':
    # Configuration
    LLM_API_BASE = "https://stargate-cetus.prod.tardis.telekom.de/nwi/dnamits/dnabot-llm-api/v1/chat/completions"
    TOKEN_URL = "https://iris-cetus.prod.tardis.telekom.de/auth/realms/default/protocol/openid-connect/token"  # Keycloak token endpoint
    MODEL_NAME = "Mistral-Small-3.2-24B-Instruct-2506"  # Raw model name without provider prefix
    # MODEL_NAME = "Meta-Llama-3.1-70B-Instruct"  # Raw model name without provider prefix

    # Set dummy credentials if not provided
    if not os.environ.get("DNABOT_OAUTH_CLIENT_ID"):
        print("Warning: DNABOT_OAUTH_CLIENT_ID not set. Using a dummy value.")
        os.environ["DNABOT_OAUTH_CLIENT_ID"] = "dummy-client-id"
    if not os.environ.get("DNABOT_OAUTH_CLIENT_SECRET"):
        print("Warning: DNABOT_OAUTH_CLIENT_SECRET not set. Using a dummy value.")
        os.environ["DNABOT_OAUTH_CLIENT_SECRET"] = "dummy-secret"

    try:
        # Use the direct approach with SSL verification disabled and no proxy
        oauth_lm = DNABot(
            model=MODEL_NAME,
            token_url=TOKEN_URL,
            api_base=LLM_API_BASE,
            verify_ssl=False,  # Disable SSL verification for self-signed certificates
            use_proxy=False,   # Disable proxy for direct connection
            temperature=1.2,  # Match your curl example
            max_tokens=300,   # Match your curl example
            top_p=1,
            frequency_penalty=0,
            response_format={"type": "json_object"},
            user="Juan"  # Optional user identifier
        )

        # Configure DSPy
        lm = dspy.LM("openai/gpt-4.1-nano")

        dspy.configure(lm=lm)
        # dspy.configure(lm=oauth_lm)

        # Test 1: Direct call to our LM (bypasses DSPy's Predict completely)
        print(f"\n--- Test 1: Direct LM Call ---")
        try:
            direct_result = oauth_lm("What is the capital of Spain?")
            print(f"Direct result: {direct_result}")
            if direct_result and "choices" in direct_result and direct_result["choices"]:
                print(f"Direct answer: {direct_result['choices'][0]['text']}")
        except Exception as e:
            print(f"Error in direct call: {e}")

        # Test 2: DSPy Predict (only if direct call works)
        print(f"\n--- Test 2: DSPy Predict ---")
        predictor = dspy.Predict("question -> answer")
        
        print(f"\n--- Testing Direct API Connection ---")
        print(f"Model: {MODEL_NAME}")
        print(f"Endpoint: {LLM_API_BASE}")
        
        try:
            response = predictor(question="What is the capital of Spain?")
            print(f"Question: What is the capital of Spain?")
            print(f"Answer: {response.answer}")
        except Exception as e:
            print(f"Error during DSPy prediction: {e}")
            # Print more details about the error
            import traceback
            traceback.print_exc()

    except ValueError as e:
        print(f"Configuration Error: {e}")