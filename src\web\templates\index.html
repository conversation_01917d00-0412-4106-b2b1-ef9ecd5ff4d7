<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🧠 Chain of Thought Idea Generator</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container">
        <header>
            <h1><i class="fas fa-brain"></i> Chain of Thought Idea Generator</h1>
            <p>Generate structured ideas with step-by-step reasoning</p>
        </header>

        <main>
            <!-- Input Section -->
            <section class="input-section">
                <div class="form-group">
                    <label for="topic">
                        <i class="fas fa-lightbulb"></i> Topic
                    </label>
                    <input type="text" id="topic" placeholder="Enter your topic or question..." required>
                </div>

                <div class="form-group">
                    <label for="template">
                        <i class="fas fa-template"></i> Template (Optional)
                    </label>
                    <select id="template">
                        <option value="">Select a template...</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="context">
                        <i class="fas fa-info-circle"></i> Custom Context (Optional)
                    </label>
                    <textarea id="context" placeholder="Add any additional context or constraints..."></textarea>
                </div>

                <div class="button-group">
                    <button id="generateBtn" class="generate-btn">
                        <i class="fas fa-magic"></i> Generate Ideas
                    </button>

                    <button id="validateBtn" class="validate-btn">
                        <i class="fas fa-search"></i> Generate & Validate
                    </button>
                </div>
            </section>

            <!-- Loading Section -->
            <section id="loadingSection" class="loading-section" style="display: none;">
                <div class="spinner"></div>
                <p>Generating ideas with chain of thought reasoning...</p>
            </section>

            <!-- Results Section -->
            <section id="resultsSection" class="results-section" style="display: none;">
                <div class="results-header">
                    <h2 id="resultsTopic"></h2>
                    <button id="saveBtn" class="save-btn">
                        <i class="fas fa-save"></i> Save Results
                    </button>
                </div>

                <!-- Thinking Chain -->
                <div class="thinking-chain">
                    <h3><i class="fas fa-brain"></i> Chain of Thought Process</h3>
                    <div id="thinkingSteps" class="thinking-steps"></div>
                </div>

                <!-- Bullet Points -->
                <div class="bullet-points">
                    <h3><i class="fas fa-list"></i> Generated Ideas</h3>
                    <div id="bulletPointsContainer" class="bullet-points-container"></div>
                </div>

                <!-- Summary -->
                <div class="summary">
                    <h3><i class="fas fa-chart-line"></i> Summary</h3>
                    <p id="summaryText"></p>
                </div>

                <!-- Next Steps -->
                <div class="next-steps">
                    <h3><i class="fas fa-rocket"></i> Next Steps</h3>
                    <ol id="nextStepsList"></ol>
                </div>

                <!-- Validation Results -->
                <div id="validationResults" class="validation-results" style="display: none;">
                    <h3><i class="fas fa-check-circle"></i> Ground Truth Validation</h3>
                    <div class="validation-scores">
                        <div class="score-item">
                            <span class="score-label">Overall Score:</span>
                            <span id="overallScore" class="score-value"></span>
                        </div>
                        <div class="score-item">
                            <span class="score-label">Concept Overlap:</span>
                            <span id="conceptScore" class="score-value"></span>
                        </div>
                        <div class="score-item">
                            <span class="score-label">Theme Alignment:</span>
                            <span id="themeScore" class="score-value"></span>
                        </div>
                        <div class="score-item">
                            <span class="score-label">Recommendation Quality:</span>
                            <span id="qualityScore" class="score-value"></span>
                        </div>
                        <div class="score-item">
                            <span class="score-label">Novelty Score:</span>
                            <span id="noveltyScore" class="score-value"></span>
                        </div>
                    </div>
                    <div class="ground-truth-info">
                        <h4>Ground Truth Data:</h4>
                        <div id="groundTruthInfo"></div>
                    </div>
                </div>
            </section>

            <!-- Error Section -->
            <section id="errorSection" class="error-section" style="display: none;">
                <div class="error-message">
                    <i class="fas fa-exclamation-triangle"></i>
                    <span id="errorText"></span>
                </div>
            </section>
        </main>
    </div>

    <script src="{{ url_for('static', filename='js/app.js') }}"></script>
</body>
</html>
