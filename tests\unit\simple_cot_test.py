"""
Simple non-interactive test for Chain of Thought idea generation.
"""

from cot_idea_generator import CoTIdeaSystem


def main():
    """Run a simple CoT test."""
    print("🧠 Simple Chain of Thought Test")
    print("=" * 40)
    
    # Initialize system
    system = CoTIdeaSystem()
    
    if not system.generator:
        print("❌ Failed to initialize DSPy")
        return
    
    # Test case
    topic = "improving AI model feedback systems"
    context = system.get_template_context("technical")
    
    print(f"🎯 Topic: {topic}")
    print(f"📝 Using technical template")
    
    try:
        # Generate ideas
        print("\n🔄 Generating ideas...")
        ideas = system.generate_ideas(topic=topic, context=context)
        
        # Display results
        system.display_ideas(ideas)
        
        # Save results
        filename = "simple_cot_test_results.json"
        system.save_ideas(ideas, filename)
        
        print("\n✅ Test completed successfully!")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
