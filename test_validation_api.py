"""
Test the validation API endpoint.
"""

import requests
import json
import time

def test_validation_api():
    """Test the validation API endpoint."""
    print("🧪 Testing Validation API")
    print("=" * 30)
    
    base_url = "http://localhost:5000"
    
    # Test data
    test_data = {
        "topic": "web application performance optimization",
        "context": "frontend techniques and best practices",
        "num_results": 3
    }
    
    try:
        print("🔍 Testing validation endpoint...")
        print(f"📝 Topic: {test_data['topic']}")
        print(f"🎯 Context: {test_data['context']}")
        
        # Make API request
        response = requests.post(
            f"{base_url}/api/validate",
            json=test_data,
            timeout=60
        )
        
        print(f"📊 Response Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            
            # Display results
            print("\n✅ Validation Results:")
            print("-" * 40)
            
            validation = data['validation_score']
            print(f"🎯 Overall Score: {validation['overall_score']:.2f}")
            print(f"📊 Concept Overlap: {validation['concept_overlap']:.2f}")
            print(f"🎨 Theme Alignment: {validation['theme_alignment']:.2f}")
            print(f"⭐ Recommendation Quality: {validation['recommendation_quality']:.2f}")
            print(f"💡 Novelty Score: {validation['novelty_score']:.2f}")
            
            print(f"\n🧠 Generated Ideas:")
            ideas = data['ideas']
            print(f"   Topic: {ideas['topic']}")
            print(f"   Thinking Steps: {len(ideas['thinking_chain'])}")
            print(f"   Bullet Points: {len(ideas['bullet_points'])}")
            print(f"   Summary: {ideas['summary'][:100]}...")
            
            print(f"\n🔍 Ground Truth:")
            gt = data['ground_truth']
            print(f"   Search Query: {gt['query']}")
            print(f"   Key Concepts: {gt['key_concepts']}")
            print(f"   Common Themes: {gt['common_themes']}")
            print(f"   Search Results: {gt['num_search_results']}")
            
            print(f"\n📋 Detailed Feedback:")
            print(validation['detailed_feedback'][:300] + "...")
            
            print("\n🎉 API test successful!")
            return True
            
        else:
            print(f"❌ API request failed: {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ Connection failed - make sure the web app is running")
        print("💡 Start the server with: uv run python simple_web_app.py")
        return False
    except requests.exceptions.Timeout:
        print("⏰ Request timed out - validation takes time")
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def test_health_endpoint():
    """Test the health endpoint."""
    print("\n🏥 Testing Health Endpoint")
    print("-" * 30)
    
    try:
        response = requests.get("http://localhost:5000/health", timeout=5)
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Health Status: {data['status']}")
            print(f"🧠 CoT System: {data['cot_system']}")
            print(f"🔍 Validator: {data['validator']}")
            return True
        else:
            print(f"❌ Health check failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Health check error: {e}")
        return False

if __name__ == '__main__':
    print("🌐 Testing Web Application API")
    print("=" * 40)
    
    # Test health first
    health_ok = test_health_endpoint()
    
    if health_ok:
        # Test validation API
        validation_ok = test_validation_api()
        
        if validation_ok:
            print("\n🎉 All API tests passed!")
        else:
            print("\n❌ Validation API test failed")
    else:
        print("\n❌ Health check failed - server may not be running")
        print("💡 Start the server with: uv run python simple_web_app.py")
