"""
Complete setup and run script for the Chain of Thought Web Application.
This script will:
1. Verify all dependencies
2. Initialize the CoT system
3. Start the Flask server
4. Open the browser
5. Run basic tests
"""

import sys
import os
import time
import webbrowser
from pathlib import Path

def check_dependencies():
    """Check if all required dependencies are installed."""
    print("🔍 Checking dependencies...")
    
    required_packages = [
        ('flask', 'Flask'),
        ('flask_cors', 'Flask-CORS'),
        ('dspy', 'DSPy'),
        ('openai', 'OpenAI'),
        ('pydantic', 'Pydantic')
    ]
    
    missing = []
    for package, name in required_packages:
        try:
            __import__(package)
            print(f"✅ {name} - OK")
        except ImportError:
            print(f"❌ {name} - Missing")
            missing.append(name)
    
    if missing:
        print(f"\n⚠️  Missing packages: {', '.join(missing)}")
        print("Please install them with: uv add flask flask-cors")
        return False
    
    print("✅ All dependencies are installed")
    return True

def check_files():
    """Check if all required files exist."""
    print("\n📁 Checking required files...")

    # Get the repository root (two levels up from this script)
    repo_root = Path(__file__).parent.parent.parent

    required_files = [
        'src/cot_system/cot_idea_generator.py',
        'src/web/app.py',
        'src/web/templates/index.html',
        'src/web/static/css/style.css',
        'src/web/static/js/app.js'
    ]
    
    missing = []
    for file_path in required_files:
        full_path = repo_root / file_path
        if full_path.exists():
            print(f"✅ {file_path} - OK")
        else:
            print(f"❌ {file_path} - Missing")
            missing.append(file_path)
    
    if missing:
        print(f"\n⚠️  Missing files: {', '.join(missing)}")
        return False
    
    print("✅ All required files are present")
    return True

def test_cot_system():
    """Test the CoT system initialization."""
    print("\n🧠 Testing CoT system...")

    try:
        # Add the src directory to the path
        repo_root = Path(__file__).parent.parent.parent
        src_path = repo_root / 'src'
        if str(src_path) not in sys.path:
            sys.path.insert(0, str(src_path))

        from cot_system.cot_idea_generator import CoTIdeaSystem
        system = CoTIdeaSystem()

        if system.generator:
            print("✅ CoT system initialized successfully")
            return True
        else:
            print("❌ CoT system failed to initialize")
            return False

    except Exception as e:
        print(f"❌ CoT system error: {e}")
        import traceback
        traceback.print_exc()
        return False

def start_server():
    """Start the Flask server."""
    print("\n🚀 Starting Flask server...")

    try:
        # Add the src directory to the path
        repo_root = Path(__file__).parent.parent.parent
        src_path = repo_root / 'src'
        if str(src_path) not in sys.path:
            sys.path.insert(0, str(src_path))

        from web.app import app, initialize_system
        
        # Initialize the system
        if not initialize_system():
            print("❌ Failed to initialize system")
            return False
        
        print("✅ System initialized successfully")
        print("🌐 Starting server on http://localhost:5000")
        print("🔗 Opening browser...")
        
        # Open browser after a short delay
        def open_browser():
            time.sleep(2)
            webbrowser.open('http://localhost:5000')
        
        import threading
        browser_thread = threading.Thread(target=open_browser)
        browser_thread.daemon = True
        browser_thread.start()
        
        # Start the Flask server
        app.run(debug=False, host='127.0.0.1', port=5000, use_reloader=False)
        
    except KeyboardInterrupt:
        print("\n👋 Server stopped by user")
        return True
    except Exception as e:
        print(f"❌ Server error: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main setup and run function."""
    print("🧠 Chain of Thought Idea Generator - Setup & Run")
    print("=" * 60)
    
    # Step 1: Check dependencies
    if not check_dependencies():
        return False
    
    # Step 2: Check files
    if not check_files():
        return False
    
    # Step 3: Test CoT system
    if not test_cot_system():
        return False
    
    # Step 4: Start server
    print("\n" + "=" * 60)
    print("🎉 All checks passed! Starting the web application...")
    print("📝 Instructions:")
    print("   • The browser will open automatically")
    print("   • Enter a topic to generate ideas")
    print("   • Choose a template for better results")
    print("   • Press Ctrl+C to stop the server")
    print("=" * 60)
    
    return start_server()

if __name__ == '__main__':
    success = main()
    if not success:
        print("\n❌ Setup failed. Please check the errors above.")
        sys.exit(1)
