"""
Test script to verify all web API endpoints are working correctly.
"""

import requests
import json
import time

def test_endpoint(url, method='GET', data=None, description=""):
    """Test a single endpoint."""
    print(f"\n🧪 Testing: {description}")
    print(f"📍 {method} {url}")
    
    try:
        if method == 'GET':
            response = requests.get(url, timeout=10)
        elif method == 'POST':
            response = requests.post(url, json=data, timeout=30)
        
        print(f"✅ Status: {response.status_code}")
        
        if response.status_code == 200:
            try:
                json_data = response.json()
                print(f"📄 Response: {json.dumps(json_data, indent=2)[:200]}...")
                return True, json_data
            except:
                print(f"📄 Response: {response.text[:200]}...")
                return True, response.text
        else:
            print(f"❌ Error: {response.text}")
            return False, None
            
    except requests.exceptions.ConnectionError:
        print("❌ Connection failed - server may not be running")
        return False, None
    except requests.exceptions.Timeout:
        print("⏰ Request timed out")
        return False, None
    except Exception as e:
        print(f"❌ Error: {e}")
        return False, None

def main():
    """Run all API tests."""
    print("🚀 Chain of Thought Web API Test Suite")
    print("=" * 50)
    
    base_url = "http://localhost:5000"
    
    # Test 1: Health check
    success, _ = test_endpoint(f"{base_url}/health", description="Health Check")
    
    if not success:
        print("\n❌ Server is not responding. Please ensure the Flask app is running:")
        print("   python app.py")
        return
    
    # Test 2: Main page
    test_endpoint(f"{base_url}/", description="Main Page")
    
    # Test 3: Templates API
    success, templates_data = test_endpoint(f"{base_url}/api/templates", description="Templates API")
    
    # Test 4: Generate Ideas API (simple test)
    if success:
        test_data = {
            "topic": "improving web applications",
            "template": "technical",
            "context": "Focus on performance and user experience"
        }
        
        print(f"\n🎯 Testing idea generation with topic: '{test_data['topic']}'")
        success, ideas_data = test_endpoint(
            f"{base_url}/api/generate", 
            method='POST', 
            data=test_data,
            description="Generate Ideas API"
        )
        
        if success and ideas_data:
            print("\n🎉 Idea generation successful!")
            if 'thinking_chain' in ideas_data:
                print(f"🧠 Thinking steps: {len(ideas_data['thinking_chain'])}")
            if 'bullet_points' in ideas_data:
                print(f"💡 Ideas generated: {len(ideas_data['bullet_points'])}")
            
            # Test 5: Save Ideas API
            save_data = {
                "ideas": ideas_data,
                "filename": "test_ideas.json"
            }
            
            test_endpoint(
                f"{base_url}/api/save",
                method='POST',
                data=save_data,
                description="Save Ideas API"
            )
    
    print("\n" + "=" * 50)
    print("🏁 API testing complete!")
    print("🌐 Open http://localhost:5000 in your browser to use the web interface")

if __name__ == '__main__':
    main()
