"""
Test the updated CoT system with Pydantic structure.
"""

import sys
from pathlib import Path

# Add src to path
src_path = Path(__file__).parent / 'src'
if str(src_path) not in sys.path:
    sys.path.insert(0, str(src_path))

from cot_system.cot_idea_generator import CoTIdeaSystem

def test_pydantic_cot():
    """Test the CoT system with the new Pydantic structure."""
    print("🧪 Testing Updated CoT System with Pydantic Structure")
    print("=" * 55)
    
    try:
        # Initialize system
        print("🔄 Initializing CoT system...")
        system = CoTIdeaSystem()
        print("✅ CoT system initialized")
        
        # Test idea generation
        print("\n🧠 Testing idea generation...")
        topic = "improving web application performance"
        context = "Focus on frontend optimization techniques"
        
        print(f"📝 Topic: {topic}")
        print(f"🎯 Context: {context}")
        
        # Generate ideas
        ideas = system.generate_ideas(topic=topic, context=context)
        
        print("\n📊 Results:")
        print(f"🎯 Topic: {ideas.topic}")
        print(f"🧠 Thinking steps: {len(ideas.thinking_chain)}")
        print(f"💡 Bullet points: {len(ideas.bullet_points)}")
        print(f"📝 Summary length: {len(ideas.summary)} chars")
        print(f"🚀 Next steps: {len(ideas.next_steps)}")
        
        # Show first thinking step
        if ideas.thinking_chain:
            first_step = ideas.thinking_chain[0]
            print(f"\n🔍 First thinking step:")
            print(f"   Step {first_step.step_number}: {first_step.thought[:100]}...")
            print(f"   Category: {first_step.category}")
            print(f"   Confidence: {first_step.confidence}")
        
        # Show first bullet point
        if ideas.bullet_points:
            first_bp = ideas.bullet_points[0]
            print(f"\n💡 First bullet point:")
            print(f"   Content: {first_bp.content[:100]}...")
            print(f"   Priority: {first_bp.priority}")
            print(f"   Category: {first_bp.category}")
            print(f"   Evidence: {len(first_bp.supporting_evidence)} items")
            print(f"   Concepts: {len(first_bp.related_concepts)} items")
        
        print(f"\n📋 Summary: {ideas.summary[:150]}...")
        
        print("\n🎉 Test completed successfully!")
        print("✅ Pydantic structure is working correctly")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    success = test_pydantic_cot()
    if not success:
        sys.exit(1)
