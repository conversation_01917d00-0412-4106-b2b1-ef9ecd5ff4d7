"""
Main module for style extraction pipeline.
Refactored version with modular components - Async version.
"""

import dspy
import json
import pickle
import os
import asyncio
from datetime import datetime
from dspy import Signature

# Import from our new modules
from models.style_extraction import StyleAnalysis, StyleAnalysisMetadata
from utils.data_utils import gen_train_dev_data, load_examples, generate_examples_from_images_async
from evaluation import style_score, run_evaluation_async


# Define the signature at module level to avoid pickling issues
class ExtractStyleSignature(Signature):
    """
    Your goal is to create scene-agnostic style definitions that can be applied to any subject matter for AI image generation.

    # Core Directive
    Extract HOW visual elements are presented, never WHAT is being presented. Focus on visual principles, not visual content.

    # Validation Requirements

    Before finalizing any style description, verify:

    1. **Universal Applicability Test:** Could this description work for portraits, landscapes, abstracts, and still lifes?
    2. **Abstraction Verification:** Are all terms sufficiently abstract and scene-independent?
    3. **Content Elimination Check:** Have all specific content references been removed?
    4. **Principle Focus:** Does each point describe a visual principle rather than visual content?

    Respond with a JSON object.
    """
    instruction = dspy.InputField()
    style = dspy.OutputField(desc=f"A single {StyleAnalysis.model_json_schema()}")
    metadata = dspy.OutputField(desc=f"{StyleAnalysisMetadata.model_json_schema()}")


def setup_dspy():
    """Configure DSPy settings and language model."""
    dspy.settings.configure(track_usage=True)
    dspy.enable_logging()


def create_predictor():
    """Create the style extraction predictor with signature."""
    predictor = dspy.Predict(ExtractStyleSignature)
    return predictor


def load_training_data(examples_file="./examples/style_examples.jsonl"):
    """Load and prepare training and development datasets."""
    print(f"Loading training data from {examples_file}...")
    examples = load_examples(examples_file)
    trainset, devset = gen_train_dev_data(examples)
    print(f"Loaded {len(trainset)} training examples and {len(devset)} dev examples")
    return trainset, devset


def optimize_predictor_labeled_fewshot(predictor, trainset, k=5):
    """Apply LabeledFewShot optimization to the predictor."""
    print(f"Applying LabeledFewShot optimization with k={k}...")
    optimizer = dspy.LabeledFewShot(k=k)
    optimized_predictor = optimizer.compile(predictor, trainset=trainset)

    print(f"Original predictor demos: {len(predictor.demos)}")
    print(f"Optimized predictor demos: {len(optimized_predictor.demos)}")

    return optimized_predictor


def optimize_predictor_bootstrap_fewshot(predictor, trainset, max_labeled_demos=4, max_bootstrapped_demos=4, metric=None):
    """Apply BootstrapFewShot optimization to the predictor."""
    if metric is None:
        metric = style_score

    print(f"Applying BootstrapFewShot optimization (labeled: {max_labeled_demos}, bootstrapped: {max_bootstrapped_demos})...")
    optimizer = dspy.BootstrapFewShot(
        max_labeled_demos=max_labeled_demos,
        max_bootstrapped_demos=max_bootstrapped_demos,
        metric=metric
    )
    optimized_predictor = optimizer.compile(predictor, trainset=trainset)

    print(f"Original predictor demos: {len(predictor.demos)}")
    print(f"Optimized predictor demos: {len(optimized_predictor.demos)}")

    return optimized_predictor


def optimize_predictor_bootstrap_random_search(predictor, trainset, max_labeled_demos=4, max_bootstrapped_demos=4,
                                             num_candidate_programs=10, num_threads=4, metric=None):
    """Apply BootstrapFewShotWithRandomSearch optimization to the predictor."""
    if metric is None:
        metric = style_score

    print(f"Applying BootstrapFewShotWithRandomSearch optimization...")
    print(f"Config: labeled={max_labeled_demos}, bootstrapped={max_bootstrapped_demos}, candidates={num_candidate_programs}")

    optimizer = dspy.BootstrapFewShotWithRandomSearch(
        max_labeled_demos=max_labeled_demos,
        max_bootstrapped_demos=max_bootstrapped_demos,
        num_candidate_programs=num_candidate_programs,
        num_threads=num_threads,
        metric=metric
    )
    optimized_predictor = optimizer.compile(predictor, trainset=trainset)

    print(f"Original predictor demos: {len(predictor.demos)}")
    print(f"Optimized predictor demos: {len(optimized_predictor.demos)}")

    return optimized_predictor


def optimize_predictor_copro(predictor, trainset, depth=3, metric=None):
    """Apply COPRO (Coordinate Ascent Prompt Optimization) to the predictor."""
    if metric is None:
        metric = style_score

    print(f"Applying COPRO optimization with depth={depth}...")
    optimizer = dspy.COPRO(depth=depth, metric=metric)

    # COPRO requires eval_kwargs parameter
    eval_kwargs = {
        'num_threads': 1,  # COPRO works better with single threading
        'display_progress': True,
        'display_table': 0
    }

    optimized_predictor = optimizer.compile(
        predictor,
        trainset=trainset,
        eval_kwargs=eval_kwargs
    )

    print(f"Original predictor demos: {len(predictor.demos)}")
    print(f"Optimized predictor demos: {len(optimized_predictor.demos)}")

    return optimized_predictor


def optimize_predictor_miprov2(predictor, trainset, auto="light", num_threads=4, metric=None,
                              max_bootstrapped_demos=2, max_labeled_demos=2):
    """Apply MIPROv2 (Multi-prompt Instruction Proposal Optimizer v2) to the predictor."""
    if metric is None:
        metric = style_score

    print(f"Applying MIPROv2 optimization (auto={auto}, threads={num_threads})...")
    optimizer = dspy.MIPROv2(
        metric=metric,
        auto=auto,
        num_threads=num_threads
    )
    optimized_predictor = optimizer.compile(
        predictor,
        trainset=trainset,
        max_bootstrapped_demos=max_bootstrapped_demos,
        max_labeled_demos=max_labeled_demos,
        requires_permission_to_run=False  # Bypass interactive confirmation
    )

    print(f"Original predictor demos: {len(predictor.demos)}")
    print(f"Optimized predictor demos: {len(optimized_predictor.demos)}")

    return optimized_predictor


async def evaluate_predictors_async(predictor, optimized_predictor, devset, num_threads=24, display_table=5, optimizer_name="Optimized"):
    """Evaluate both original and optimized predictors asynchronously."""
    print("\n=== Evaluating Original Predictor ===")
    original_score = await run_evaluation_async(predictor, devset, style_score, num_threads=num_threads, display_table=display_table)
    print(f"Original Metric: {original_score}")

    if optimized_predictor is not None:
        print(f"\n=== Evaluating {optimizer_name} Predictor ===")
        optimized_score = await run_evaluation_async(optimized_predictor, devset, style_score, num_threads=num_threads, display_table=display_table)
        print(f"{optimizer_name} Metric: {optimized_score}")

        # Handle tuple return from evaluation (score, individual_scores)
        if isinstance(original_score, tuple):
            orig_avg = original_score[0]
        else:
            orig_avg = original_score

        if isinstance(optimized_score, tuple):
            opt_avg = optimized_score[0]
        else:
            opt_avg = optimized_score

        improvement = opt_avg - orig_avg
        print(f"\nImprovement: {improvement:.2f} points")

        return original_score, optimized_score, improvement
    else:
        return original_score, None, None


def evaluate_predictors(predictor, optimized_predictor, devset, num_threads=24, display_table=5, optimizer_name="Optimized"):
    """Evaluate both original and optimized predictors (sync wrapper)."""
    return asyncio.run(evaluate_predictors_async(predictor, optimized_predictor, devset, num_threads, display_table, optimizer_name))


async def run_optimization_comparison_async(predictor, trainset, devset, optimizers_to_run=None, num_threads=24, display_table=5):
    """Run multiple optimizers and compare their performance asynchronously."""
    if optimizers_to_run is None:
        optimizers_to_run = ["labeled_fewshot", "bootstrap_fewshot", "bootstrap_random_search"]

    print("\n" + "="*80)
    print("OPTIMIZATION COMPARISON")
    print("="*80)

    results = {}

    # Evaluate original predictor first
    print("\n=== Evaluating Original Predictor ===")
    original_score = await run_evaluation_async(predictor, devset, style_score, num_threads=num_threads, display_table=display_table)
    print(f"Original Metric: {original_score}")
    results["original"] = original_score

    # Run each optimizer
    for optimizer_name in optimizers_to_run:
        print(f"\n" + "-"*60)
        print(f"Running {optimizer_name.upper()} Optimization")
        print("-"*60)

        try:
            # Run optimization in thread pool to avoid blocking
            loop = asyncio.get_event_loop()

            if optimizer_name == "labeled_fewshot":
                optimized_predictor = await loop.run_in_executor(None, lambda: optimize_predictor_labeled_fewshot(predictor, trainset, k=5))
            elif optimizer_name == "bootstrap_fewshot":
                optimized_predictor = await loop.run_in_executor(None, lambda: optimize_predictor_bootstrap_fewshot(predictor, trainset,
                                                                         max_labeled_demos=3, max_bootstrapped_demos=3))
            elif optimizer_name == "bootstrap_random_search":
                optimized_predictor = await loop.run_in_executor(None, lambda: optimize_predictor_bootstrap_random_search(predictor, trainset,
                                                                               max_labeled_demos=2, max_bootstrapped_demos=2,
                                                                               num_candidate_programs=5, num_threads=num_threads))
            elif optimizer_name == "copro":
                optimized_predictor = await loop.run_in_executor(None, lambda: optimize_predictor_copro(predictor, trainset, depth=2))
            elif optimizer_name == "miprov2":
                optimized_predictor = await loop.run_in_executor(None, lambda: optimize_predictor_miprov2(predictor, trainset, auto="light", num_threads=num_threads))
            else:
                print(f"Unknown optimizer: {optimizer_name}")
                continue

            # Evaluate optimized predictor
            print(f"\n=== Evaluating {optimizer_name.upper()} Optimized Predictor ===")
            optimized_score = await run_evaluation_async(optimized_predictor, devset, style_score,
                                           num_threads=num_threads, display_table=display_table)
            print(f"{optimizer_name.upper()} Metric: {optimized_score}")
            results[optimizer_name] = optimized_score

            # Calculate improvement
            if isinstance(original_score, tuple):
                orig_avg = original_score[0]
            else:
                orig_avg = original_score

            if isinstance(optimized_score, tuple):
                opt_avg = optimized_score[0]
            else:
                opt_avg = optimized_score

            improvement = opt_avg - orig_avg
            print(f"Improvement over original: {improvement:.2f} points")

        except Exception as e:
            print(f"Error running {optimizer_name}: {e}")
            results[optimizer_name] = f"Error: {e}"

    # Print summary
    print(f"\n" + "="*80)
    print("OPTIMIZATION RESULTS SUMMARY")
    print("="*80)

    for name, score in results.items():
        if isinstance(score, tuple):
            print(f"{name.upper():20}: {score[0]:.2f}%")
        elif isinstance(score, (int, float)):
            print(f"{name.upper():20}: {score:.2f}%")
        else:
            print(f"{name.upper():20}: {score}")

    return results


def run_optimization_comparison(predictor, trainset, devset, optimizers_to_run=None, num_threads=24, display_table=5):
    """Run multiple optimizers and compare their performance (sync wrapper)."""
    return asyncio.run(run_optimization_comparison_async(predictor, trainset, devset, optimizers_to_run, num_threads, display_table))


def test_single_prediction(predictor, optimized_predictor, trainset, test_index=20):
    """Test both predictors on a single example."""
    print("\n=== Testing Single Prediction ===")

    if test_index >= len(trainset):
        test_index = 0
        print(f"Test index adjusted to {test_index} (trainset has {len(trainset)} examples)")

    test_instruction = trainset[test_index].instruction

    print("Original predictor response:")
    try:
        original_response = predictor(instruction=test_instruction)
        try:
            original_metadata = json.loads(original_response.metadata)
            print(f"Style name: {original_metadata.get('style_name', 'N/A')}")
        except (json.JSONDecodeError, TypeError) as e:
            print(f"⚠️ JSON parsing error for original metadata: {e}")
            print(f"Raw metadata: {str(original_response.metadata)[:200]}...")
    except Exception as e:
        print(f"❌ Error with original predictor: {e}")
        original_response = None

    print("\nOptimized predictor response:")
    try:
        optimized_response = optimized_predictor(instruction=test_instruction)
        try:
            optimized_metadata = json.loads(optimized_response.metadata)
            print(f"Style name: {optimized_metadata.get('style_name', 'N/A')}")
        except (json.JSONDecodeError, TypeError) as e:
            print(f"⚠️ JSON parsing error for optimized metadata: {e}")
            print(f"Raw metadata: {str(optimized_response.metadata)[:200]}...")
    except Exception as e:
        print(f"❌ Error with optimized predictor: {e}")
        optimized_response = None

    print(f"\nBoth predictors tested on: {test_instruction[:100]}...")
    print("✅ Single prediction test completed")

    return original_response, optimized_response


async def generate_new_examples_async(predictor, images_file="./examples/style_images_list.json", output_file="./examples/style_examples.jsonl"):
    """Generate new examples from images using the predictor asynchronously."""
    print(f"\n=== Generating New Examples ===")
    print(f"Processing images from {images_file}...")
    examples = await generate_examples_from_images_async(predictor, images_file, output_file)
    print(f"Generated {len(examples)} new examples")
    return examples


def generate_new_examples(predictor, images_file="./examples/style_images_list.json", output_file="./examples/style_examples.jsonl"):
    """Generate new examples from images using the predictor (sync wrapper)."""
    return asyncio.run(generate_new_examples_async(predictor, images_file, output_file))


def save_optimized_predictor(predictor, optimizer_name, performance_score, save_dir="saved_predictors"):
    """
    Save an optimized predictor to disk with metadata.

    Args:
        predictor: The optimized DSPy predictor to save
        optimizer_name: Name of the optimizer used (e.g., "LabeledFewShot")
        performance_score: The performance score achieved
        save_dir: Directory to save the predictor

    Returns:
        str: Path to the saved predictor file
    """
    # Create save directory if it doesn't exist
    os.makedirs(save_dir, exist_ok=True)

    # Generate filename with timestamp
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"{optimizer_name}_{performance_score:.1f}pct_{timestamp}"
    filepath = os.path.join(save_dir, filename)

    # Prepare metadata
    metadata = {
        "optimizer_name": optimizer_name,
        "performance_score": performance_score,
        "timestamp": timestamp,
        "num_demos": len(predictor.demos) if hasattr(predictor, 'demos') else 0,
        "predictor_type": type(predictor).__name__
    }

    # Use DSPy's built-in save functionality
    try:
        predictor.save(filepath + ".json")

        # Save metadata separately
        metadata_filepath = filepath + "_metadata.json"
        with open(metadata_filepath, 'w') as f:
            json.dump(metadata, f, indent=2)

        print(f"✅ Saved optimized predictor to: {filepath}.json")
        print(f"📊 Metadata saved to: {metadata_filepath}")
        print(f"🎯 Performance: {performance_score:.1f}% with {metadata['num_demos']} demos")

        return filepath + ".json"

    except Exception as e:
        print(f"❌ Error saving predictor with DSPy save: {e}")
        print("🔄 Trying alternative save method...")

        # Fallback: save just the demos and metadata
        save_data = {
            "demos": predictor.demos if hasattr(predictor, 'demos') else [],
            "metadata": metadata,
            "predictor_class": "dspy.Predict",
            "signature_class": "ExtractStyleSignature"
        }

        fallback_filepath = filepath + "_demos.json"
        with open(fallback_filepath, 'w') as f:
            # Convert demos to serializable format
            serializable_demos = []
            for demo in save_data["demos"]:
                demo_dict = {}
                for key, value in demo.items():
                    demo_dict[key] = str(value)
                serializable_demos.append(demo_dict)

            save_data["demos"] = serializable_demos
            json.dump(save_data, f, indent=2)

        print(f"✅ Saved predictor demos and metadata to: {fallback_filepath}")
        return fallback_filepath


def load_optimized_predictor(filepath):
    """
    Load a saved optimized predictor from disk.

    Args:
        filepath: Path to the saved predictor file

    Returns:
        tuple: (predictor, metadata)
    """
    try:
        # Try loading with DSPy's built-in load
        if filepath.endswith('.json') and not filepath.endswith('_demos.json'):
            predictor = dspy.Predict(ExtractStyleSignature)
            predictor.load(filepath)

            # Load metadata
            metadata_filepath = filepath.replace('.json', '_metadata.json')
            with open(metadata_filepath, 'r') as f:
                metadata = json.load(f)
        else:
            # Load from demos file
            if not filepath.endswith('_demos.json'):
                filepath = filepath.replace('.json', '_demos.json')

            with open(filepath, 'r') as f:
                save_data = json.load(f)

            # Recreate predictor
            predictor = dspy.Predict(ExtractStyleSignature)

            # Restore demos
            demos = []
            for demo_dict in save_data["demos"]:
                demo = dspy.Example(**demo_dict)
                demos.append(demo)

            predictor.demos = demos
            metadata = save_data["metadata"]

        print(f"✅ Loaded optimized predictor from: {filepath}")
        print(f"🔧 Optimizer: {metadata['optimizer_name']}")
        print(f"🎯 Performance: {metadata['performance_score']:.1f}%")
        print(f"📚 Demos: {metadata['num_demos']}")
        print(f"📅 Created: {metadata['timestamp']}")

        return predictor, metadata

    except Exception as e:
        print(f"❌ Error loading predictor: {e}")
        return None, None


def list_saved_predictors(save_dir="saved_predictors"):
    """
    List all saved predictors with their metadata.

    Args:
        save_dir: Directory containing saved predictors

    Returns:
        list: List of predictor info dictionaries
    """
    if not os.path.exists(save_dir):
        print(f"No saved predictors directory found: {save_dir}")
        return []

    predictors = []
    for filename in os.listdir(save_dir):
        if filename.endswith('_metadata.json'):
            filepath = os.path.join(save_dir, filename)
            try:
                with open(filepath, 'r') as f:
                    metadata = json.load(f)

                predictor_filepath = filepath.replace('_metadata.json', '.pkl')
                metadata['filepath'] = predictor_filepath
                predictors.append(metadata)
            except Exception as e:
                print(f"Error reading metadata from {filepath}: {e}")

    # Sort by performance score (descending)
    predictors.sort(key=lambda x: x.get('performance_score', 0), reverse=True)

    print(f"\n📁 Found {len(predictors)} saved predictors in {save_dir}:")
    print("-" * 80)
    for i, pred in enumerate(predictors, 1):
        print(f"{i}. {pred['optimizer_name']} - {pred['performance_score']:.1f}% ({pred['num_demos']} demos) - {pred['timestamp']}")

    return predictors


def create_compiled_program(optimized_predictor, program_name="OptimizedStyleExtractor"):
    """
    Create a compiled DSPy program from an optimized predictor.

    Args:
        optimized_predictor: The optimized predictor
        program_name: Name for the compiled program class

    Returns:
        class: A compiled DSPy program class
    """

    class CompiledStyleExtractor(dspy.Module):
        def __init__(self, optimized_predictor):
            super().__init__()
            self.predictor = optimized_predictor

        def forward(self, instruction):
            """Forward pass using the optimized predictor."""
            return self.predictor(instruction=instruction)

        def __call__(self, instruction):
            """Make the program callable."""
            return self.forward(instruction)

    # Create an instance of the compiled program
    compiled_program = CompiledStyleExtractor(optimized_predictor)

    print(f"✅ Created compiled program: {program_name}")
    print(f"📚 Using predictor with {len(optimized_predictor.demos)} demos")
    print(f"🚀 Ready for production use!")

    return compiled_program


async def main_async():
    """Main async function for style extraction pipeline."""
    # Setup
    setup_dspy()
    lm = dspy.LM("openai/gpt-4o-mini", cache_in_memory=False)
    dspy.configure(lm=lm)

    predictor = create_predictor()
    trainset, devset = load_training_data()
    print(f"Loaded {len(trainset)} training examples and {len(devset)} dev examples")

    # Choose what to run (uncomment the sections you want):

    # 1. Basic evaluation only
    await evaluate_predictors_async(predictor, None, devset, num_threads=24, display_table=5)

    # 2. Single optimizer test (choose one):
    # optimized_predictor = optimize_predictor_labeled_fewshot(predictor, trainset, k=5)
    # optimized_predictor = optimize_predictor_bootstrap_fewshot(predictor, trainset)
    # optimized_predictor = optimize_predictor_bootstrap_random_search(predictor, trainset)
    # optimized_predictor = optimize_predictor_copro(predictor, trainset, depth=2)
    # optimized_predictor = optimize_predictor_miprov2(predictor, trainset, auto="light")
    # original_score, optimized_score, improvement = await evaluate_predictors_async(predictor, optimized_predictor, devset, num_threads=24, display_table=5, optimizer_name="LabeledFewShot")

    # Save the optimized predictor (example code - uncomment and modify as needed)
    # optimized_score = None
    # if optimized_score is not None:
    #     performance = optimized_score[0] if isinstance(optimized_score, tuple) else optimized_score
    #     saved_path = save_optimized_predictor(optimized_predictor, "LabeledFewShot", performance)
    #
    #     # Create a compiled program from the optimized predictor
    #     compiled_program = create_compiled_program(optimized_predictor, "LabeledFewShotStyleExtractor")
    #
    #     # Demonstrate loading the saved predictor
    #     print("\n" + "="*60)
    #     print("DEMONSTRATING SAVE/LOAD FUNCTIONALITY")
    #     print("="*60)
    #     loaded_predictor, metadata = load_optimized_predictor(saved_path)
    #
    #     # Show that loaded predictor works the same
    #     test_instruction = trainset[0].instruction
    #     original_result = compiled_program(test_instruction)
    #     loaded_compiled = create_compiled_program(loaded_predictor, "LoadedStyleExtractor")
    #     loaded_result = loaded_compiled(test_instruction)
    #
    #     print(f"\n🔍 Test Results Comparison:")
    #     print(f"Original compiled result style_name: {json.loads(original_result.metadata)['style_name']}")
    #     print(f"Loaded compiled result style_name: {json.loads(loaded_result.metadata)['style_name']}")
    #     print(f"✅ Results match: {original_result.metadata == loaded_result.metadata}")

    # List all saved predictors
    print("\n" + "="*60)
    print("SAVED PREDICTORS INVENTORY")
    print("="*60)
    _ = list_saved_predictors()  # Use underscore to indicate intentionally unused

    # 3. Compare multiple optimizers (recommended for comprehensive analysis)
    # await run_optimization_comparison_async(
    #     predictor, trainset, devset,
    #     optimizers_to_run=["labeled_fewshot", "bootstrap_fewshot"],  # Add more: "bootstrap_random_search", "copro", "miprov2"
    #     num_threads=24, display_table=5
    # )

    # 4. Single prediction test (uncomment to enable)
    # test_single_prediction(predictor, optimized_predictor, trainset, test_index=29)

    # 5. Generate new examples (uncomment to run)
    # await generate_new_examples_async(predictor)


def main():
    """Main function for style extraction pipeline (sync wrapper)."""
    asyncio.run(main_async())


if __name__ == '__main__':
    main()
