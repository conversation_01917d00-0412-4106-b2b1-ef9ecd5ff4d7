"""
Direct test of enhanced feedback optimization with 10 examples.
"""

import asyncio
import json
import os
from datetime import datetime
import dspy
from dspy import Example

# Import our modules
from main import setup_dspy, create_predictor, optimize_predictor_labeled_fewshot
from utils.data_utils import load_examples
from simple_feedback_test import generate_image_from_style, analyze_generated_image, calculate_style_similarity


async def run_enhanced_test():
    """Run enhanced feedback test with 10 examples."""
    print("🚀 Enhanced Feedback Optimization with 10 Examples")
    print("=" * 60)
    print("💰 Estimated cost: $0.40 for 10 images")
    
    # Setup DSPy
    print("\n1️⃣ Setting up DSPy...")
    setup_dspy()
    lm = dspy.LM("openai/gpt-4o-mini", cache_in_memory=False)
    dspy.configure(lm=lm)
    original_predictor = create_predictor()
    print("✅ DSPy setup complete")
    
    # Load examples
    print("\n2️⃣ Loading examples...")
    examples_file = "examples/groovjones_styles/tile_examples.jsonl"
    all_examples = load_examples(examples_file)
    
    # Select diverse examples (first 10 from different styles)
    selected_examples = []
    used_styles = set()
    
    for example in all_examples:
        if len(selected_examples) >= 10:
            break
        try:
            metadata = json.loads(example["metadata"])
            style_name = metadata.get("style_name", "unknown")
            if style_name not in used_styles:
                selected_examples.append(example)
                used_styles.add(style_name)
        except:
            continue
    
    # Fill remaining slots if needed
    while len(selected_examples) < 10 and len(selected_examples) < len(all_examples):
        for example in all_examples:
            if example not in selected_examples:
                selected_examples.append(example)
                if len(selected_examples) >= 10:
                    break
    
    print(f"✅ Selected {len(selected_examples)} examples from {len(used_styles)} different styles")
    
    # Create output directory
    output_dir = "enhanced_test_10_examples"
    os.makedirs(output_dir, exist_ok=True)
    
    # Collect feedback data
    print("\n3️⃣ Collecting feedback data...")
    feedback_data = []
    
    for i, example in enumerate(selected_examples, 1):
        try:
            print(f"\n--- Processing Example {i}/10 ---")
            
            # Extract data
            original_instruction = example["instruction"]
            expected_style = example["style"]
            expected_metadata = example["metadata"]
            
            # Get style name
            metadata = json.loads(expected_metadata)
            style_name = metadata.get("style_name", f"style_{i}").replace(" ", "_")
            
            print(f"🎨 Style: {style_name}")
            
            # Generate image
            timestamp = datetime.now().strftime("%H%M%S")
            image_path = os.path.join(output_dir, f"feedback_{style_name}_{i:02d}_{timestamp}.png")
            
            await generate_image_from_style(expected_style, image_path)
            
            # Analyze with original predictor
            print(f"🔍 Analyzing generated image...")
            actual_analysis = await analyze_generated_image(original_predictor, image_path)
            
            # Calculate similarity
            similarity = calculate_style_similarity(expected_style, actual_analysis["style"])
            
            print(f"📊 Similarity: {similarity['overall_score']:.3f}")
            
            # Store feedback data
            feedback_item = {
                "instruction": original_instruction,
                "expected_style": expected_style,
                "actual_style": actual_analysis["style"],
                "similarity_score": similarity["overall_score"],
                "style_name": style_name,
                "image_path": image_path,
                "index": i
            }
            
            feedback_data.append(feedback_item)
            
            # Show running average
            avg_similarity = sum(item["similarity_score"] for item in feedback_data) / len(feedback_data)
            print(f"📈 Running average: {avg_similarity:.3f}")
            
        except Exception as e:
            print(f"❌ Error processing example {i}: {e}")
            continue
    
    if not feedback_data:
        print("❌ No feedback data collected")
        return
    
    print(f"\n✅ Collected {len(feedback_data)} feedback items")
    avg_before = sum(item["similarity_score"] for item in feedback_data) / len(feedback_data)
    print(f"📊 Average similarity before optimization: {avg_before:.3f}")
    
    # Create optimization examples
    print("\n4️⃣ Creating optimization examples...")
    optimization_examples = []
    
    for feedback in feedback_data:
        example = Example(
            instruction=feedback["instruction"],
            style=feedback["expected_style"]
        ).with_inputs('instruction')
        optimization_examples.append(example)
    
    print(f"✅ Created {len(optimization_examples)} optimization examples")
    
    # Optimize predictor
    print("\n5️⃣ Optimizing predictor...")
    try:
        k = min(len(optimization_examples), 10)  # Use up to 10 examples
        print(f"   Using k={k} examples for LabeledFewShot optimization")
        
        optimized_predictor = optimize_predictor_labeled_fewshot(
            original_predictor,
            optimization_examples,
            k=k
        )
        print("✅ Predictor optimization complete")
        
    except Exception as e:
        print(f"❌ Error optimizing predictor: {e}")
        optimized_predictor = original_predictor
    
    # Test optimized predictor
    print("\n6️⃣ Testing optimized predictor...")
    test_results = []
    
    for i, feedback in enumerate(feedback_data, 1):
        try:
            print(f"   Testing {i}/{len(feedback_data)}: {feedback['style_name']}")
            
            # Analyze with optimized predictor
            optimized_analysis = await analyze_generated_image(
                optimized_predictor, 
                feedback["image_path"]
            )
            
            # Calculate new similarity
            new_similarity = calculate_style_similarity(
                feedback["expected_style"], 
                optimized_analysis["style"]
            )
            
            improvement = new_similarity["overall_score"] - feedback["similarity_score"]
            
            test_result = {
                "style_name": feedback["style_name"],
                "original_similarity": feedback["similarity_score"],
                "optimized_similarity": new_similarity["overall_score"],
                "improvement": improvement
            }
            
            test_results.append(test_result)
            
        except Exception as e:
            print(f"   ❌ Error testing {feedback['style_name']}: {e}")
            continue
    
    # Calculate results
    if test_results:
        original_scores = [r["original_similarity"] for r in test_results]
        optimized_scores = [r["optimized_similarity"] for r in test_results]
        improvements = [r["improvement"] for r in test_results]
        
        avg_original = sum(original_scores) / len(original_scores)
        avg_optimized = sum(optimized_scores) / len(optimized_scores)
        avg_improvement = sum(improvements) / len(improvements)
        
        improved_count = len([i for i in improvements if i > 0])
        degraded_count = len([i for i in improvements if i < 0])
        
        print(f"\n📊 Final Results:")
        print(f"   📈 Average similarity before: {avg_original:.3f}")
        print(f"   📈 Average similarity after:  {avg_optimized:.3f}")
        print(f"   📈 Average improvement:       {avg_improvement:+.3f}")
        print(f"   ✅ Improved examples:         {improved_count}/{len(test_results)}")
        print(f"   ❌ Degraded examples:         {degraded_count}/{len(test_results)}")
        
        # Show individual results
        print(f"\n📋 Individual Results:")
        for result in test_results:
            print(f"   {result['style_name']}: {result['original_similarity']:.3f} → {result['optimized_similarity']:.3f} ({result['improvement']:+.3f})")
        
        # Save results
        results_file = os.path.join(output_dir, f"results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json")
        results = {
            "configuration": {
                "num_examples": len(feedback_data),
                "optimization_method": "LabeledFewShot",
                "k_value": k
            },
            "feedback_data": feedback_data,
            "test_results": test_results,
            "summary": {
                "average_original": avg_original,
                "average_optimized": avg_optimized,
                "average_improvement": avg_improvement,
                "improved_count": improved_count,
                "degraded_count": degraded_count
            },
            "timestamp": datetime.now().isoformat()
        }
        
        with open(results_file, 'w') as f:
            json.dump(results, f, indent=2)
        
        print(f"\n📁 Results saved to: {results_file}")
        print("✅ Enhanced test completed!")
        
    else:
        print("❌ No test results generated")


if __name__ == "__main__":
    asyncio.run(run_enhanced_test())
