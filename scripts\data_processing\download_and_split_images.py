#!/usr/bin/env python3
"""
Download images from style_examples.jsonl and split each into four 512x512 tiles.
"""

import json
import os
import requests
from PIL import Image
from urllib.parse import urlparse
import time
from pathlib import Path


def load_image_links(jsonl_file):
    """Load image links from JSONL file."""
    image_links = []
    with open(jsonl_file, 'r') as f:
        for line_num, line in enumerate(f, 1):
            line = line.strip()
            if not line:
                continue

            # Remove trailing comma if present (common in some JSONL files)
            if line.endswith(','):
                line = line[:-1]

            try:
                data = json.loads(line)
                # Check for direct image_link field
                if 'image_link' in data:
                    image_links.append(data['image_link'])
                # Check for URL in instruction field (format: "Describe the image: URL")
                elif 'instruction' in data:
                    instruction = data['instruction']
                    if instruction.startswith("Describe the image: "):
                        url = instruction.replace("Describe the image: ", "").strip()
                        image_links.append(url)
            except json.JSONDecodeError as e:
                print(f"⚠️ Error parsing line {line_num}: {e}")
                print(f"Line content: {line[:100]}...")
                continue
    return image_links


def get_filename_from_url(url):
    """Extract filename from URL."""
    parsed = urlparse(url)
    filename = os.path.basename(parsed.path)
    # Remove file extension and add our own
    name_without_ext = os.path.splitext(filename)[0]
    return name_without_ext


def download_image(url, output_dir):
    """Download image from URL."""
    try:
        print(f"Downloading: {url}")
        response = requests.get(url, timeout=30)
        response.raise_for_status()
        
        filename = get_filename_from_url(url)
        filepath = os.path.join(output_dir, f"{filename}.png")
        
        with open(filepath, 'wb') as f:
            f.write(response.content)
        
        print(f"✅ Downloaded: {filepath}")
        return filepath
        
    except Exception as e:
        print(f"❌ Error downloading {url}: {e}")
        return None


def split_image_into_tiles(image_path, output_dir):
    """Split image into four 512x512 tiles."""
    try:
        with Image.open(image_path) as img:
            # Convert to RGB if necessary
            if img.mode != 'RGB':
                img = img.convert('RGB')
            
            # Get original dimensions
            width, height = img.size
            print(f"Original image size: {width}x{height}")
            
            # Resize to 1024x1024 if not already
            if width != 1024 or height != 1024:
                print(f"Resizing from {width}x{height} to 1024x1024")
                img = img.resize((1024, 1024), Image.Resampling.LANCZOS)
            
            # Create base filename
            base_name = os.path.splitext(os.path.basename(image_path))[0]
            
            # Split into 4 tiles (2x2 grid)
            tiles = []
            for row in range(2):
                for col in range(2):
                    # Calculate crop box
                    left = col * 512
                    top = row * 512
                    right = left + 512
                    bottom = top + 512
                    
                    # Crop the tile
                    tile = img.crop((left, top, right, bottom))
                    
                    # Save tile
                    tile_filename = f"{base_name}_tile_{row}_{col}.png"
                    tile_path = os.path.join(output_dir, tile_filename)
                    tile.save(tile_path, 'PNG')
                    tiles.append(tile_path)
                    
                    print(f"  ✅ Created tile: {tile_filename}")
            
            return tiles
            
    except Exception as e:
        print(f"❌ Error splitting image {image_path}: {e}")
        return []


def main():
    """Main function to download and split images."""
    # Configuration
    jsonl_file = "examples/groovjones_styles/style_examples.jsonl"
    download_dir = "downloaded_images"
    tiles_dir = "image_tiles"
    
    # Create output directories
    os.makedirs(download_dir, exist_ok=True)
    os.makedirs(tiles_dir, exist_ok=True)
    
    print("🚀 Starting image download and splitting process...")
    print(f"📁 Downloads will be saved to: {download_dir}")
    print(f"🔲 Tiles will be saved to: {tiles_dir}")
    print("="*60)
    
    # Load image links
    try:
        image_links = load_image_links(jsonl_file)
        print(f"📋 Found {len(image_links)} images to process")
    except Exception as e:
        print(f"❌ Error loading JSONL file: {e}")
        return
    
    # Process each image
    total_tiles = 0
    successful_downloads = 0
    
    for i, url in enumerate(image_links, 1):
        print(f"\n[{i}/{len(image_links)}] Processing: {get_filename_from_url(url)}")
        
        # Download image
        downloaded_path = download_image(url, download_dir)
        if downloaded_path is None:
            continue
        
        successful_downloads += 1
        
        # Split into tiles
        tiles = split_image_into_tiles(downloaded_path, tiles_dir)
        total_tiles += len(tiles)
        
        # Small delay to be respectful to the server
        time.sleep(0.5)
    
    print("\n" + "="*60)
    print("🎉 PROCESSING COMPLETE!")
    print(f"✅ Successfully downloaded: {successful_downloads}/{len(image_links)} images")
    print(f"🔲 Total tiles created: {total_tiles}")
    print(f"📁 Original images saved in: {download_dir}")
    print(f"🔲 512x512 tiles saved in: {tiles_dir}")


if __name__ == "__main__":
    main()
