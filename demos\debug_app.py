"""
Debug version of the Flask app to test step by step.
"""

from flask import Flask, render_template, request, jsonify
from flask_cors import CORS
import json
import traceback

print("🔄 Starting debug app...")

# Create Flask app
app = Flask(__name__)
CORS(app)

print("✅ Flask app created")

@app.route('/')
def index():
    """Serve the main page."""
    print("📄 Serving index page")
    return render_template('index.html')

@app.route('/api/test')
def test_api():
    """Test API endpoint."""
    print("🧪 Test API called")
    return jsonify({"status": "success", "message": "API is working!"})

@app.route('/api/templates')
def get_templates():
    """Get available templates."""
    print("📋 Templates API called")
    templates = [
        {"id": "business", "name": "Business", "description": "Market opportunities and revenue models"},
        {"id": "technical", "name": "Technical", "description": "Architecture and scalability"},
        {"id": "creative", "name": "Creative", "description": "Innovation and user experience"}
    ]
    return jsonify({"templates": templates})

@app.route('/health')
def health_check():
    """Health check endpoint."""
    print("❤️ Health check called")
    return jsonify({"status": "healthy", "message": "Debug app is running"})

if __name__ == '__main__':
    print("🚀 Starting debug Flask server...")
    print("🌐 Server will run on http://localhost:5001")
    print("🛑 Press Ctrl+C to stop")
    print("-" * 50)
    
    try:
        app.run(debug=True, host='127.0.0.1', port=5001, use_reloader=False)
    except KeyboardInterrupt:
        print("\n👋 Debug server stopped by user")
    except Exception as e:
        print(f"❌ Debug server error: {e}")
        traceback.print_exc()
