"""
Start the Chain of Thought web application.
This script properly handles the organized repository structure.
"""

import sys
import os
import subprocess
from pathlib import Path

def main():
    """Start the web application."""
    print("🧠 Chain of Thought Idea Generator - Starting Web App")
    print("=" * 60)
    
    # Get paths
    repo_root = Path(__file__).parent
    src_path = repo_root / 'src'
    web_path = src_path / 'web'
    
    # Check if files exist
    app_file = web_path / 'app.py'
    if not app_file.exists():
        print(f"❌ Flask app not found at: {app_file}")
        return False
    
    cot_file = src_path / 'cot_system' / 'cot_idea_generator.py'
    if not cot_file.exists():
        print(f"❌ CoT system not found at: {cot_file}")
        return False
    
    print("✅ All required files found")
    print(f"📁 Repository root: {repo_root}")
    print(f"📁 Web app location: {web_path}")
    
    # Set environment variables for Python path
    env = os.environ.copy()
    current_pythonpath = env.get('PYTHONPATH', '')
    if current_pythonpath:
        env['PYTHONPATH'] = f"{src_path}{os.pathsep}{current_pythonpath}"
    else:
        env['PYTHONPATH'] = str(src_path)
    
    print(f"🔧 PYTHONPATH set to: {env['PYTHONPATH']}")
    
    try:
        print("🚀 Starting Flask server with uv...")
        print("🌐 Server will be available at: http://localhost:5000")
        print("🛑 Press Ctrl+C to stop the server")
        print("-" * 60)

        # Run the Flask app using uv from the web directory
        result = subprocess.run([
            'uv', 'run', 'python', 'app.py'
        ], cwd=str(web_path), env=env)

        return result.returncode == 0
        
    except KeyboardInterrupt:
        print("\n👋 Server stopped by user")
        return True
    except Exception as e:
        print(f"❌ Error starting server: {e}")
        return False

if __name__ == '__main__':
    success = main()
    if not success:
        print("\n❌ Failed to start web application")
        print("💡 Try running manually:")
        print("   cd src/web")
        print("   uv run python app.py")
        sys.exit(1)
