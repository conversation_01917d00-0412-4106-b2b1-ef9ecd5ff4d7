"""
Test script to demonstrate async functionality in the DSPy style extraction pipeline.
"""

import asyncio
import dspy
from main import (
    setup_dspy, create_predictor, load_training_data,
    evaluate_predictors_async, run_optimization_comparison_async,
    generate_new_examples_async, optimize_predictor_labeled_fewshot
)


async def test_async_evaluation():
    """Test async evaluation functionality."""
    print("🚀 Testing Async Evaluation")
    print("=" * 50)
    
    # Setup
    setup_dspy()
    lm = dspy.LM("openai/gpt-4o-mini", cache_in_memory=False)
    dspy.configure(lm=lm)
    
    predictor = create_predictor()
    trainset, devset = load_training_data()
    
    # Take a smaller subset for faster testing
    small_devset = devset[:5]  # Use only 5 examples for quick test
    print(f"Using {len(small_devset)} examples for quick async test")
    
    # Test async evaluation
    print("\n📊 Running async evaluation...")
    start_time = asyncio.get_event_loop().time()
    
    result = await evaluate_predictors_async(
        predictor, None, small_devset, 
        num_threads=4, display_table=2
    )
    
    end_time = asyncio.get_event_loop().time()
    print(f"⏱️ Async evaluation completed in {end_time - start_time:.2f} seconds")
    print(f"📈 Result: {result}")
    
    return result


async def test_async_optimization():
    """Test async optimization comparison."""
    print("\n🔧 Testing Async Optimization")
    print("=" * 50)
    
    # Setup
    setup_dspy()
    lm = dspy.LM("openai/gpt-4o-mini", cache_in_memory=False)
    dspy.configure(lm=lm)
    
    predictor = create_predictor()
    trainset, devset = load_training_data()
    
    # Use smaller datasets for faster testing
    small_trainset = trainset[:3]  # Use only 3 training examples
    small_devset = devset[:3]      # Use only 3 dev examples
    
    print(f"Using {len(small_trainset)} training and {len(small_devset)} dev examples")
    
    # Test async optimization comparison
    print("\n⚡ Running async optimization comparison...")
    start_time = asyncio.get_event_loop().time()
    
    results = await run_optimization_comparison_async(
        predictor, small_trainset, small_devset,
        optimizers_to_run=["labeled_fewshot"],  # Test just one optimizer
        num_threads=2, display_table=1
    )
    
    end_time = asyncio.get_event_loop().time()
    print(f"⏱️ Async optimization completed in {end_time - start_time:.2f} seconds")
    print(f"📊 Results: {results}")
    
    return results


async def test_async_example_generation():
    """Test async example generation (if image files exist)."""
    print("\n🖼️ Testing Async Example Generation")
    print("=" * 50)
    
    # Setup
    setup_dspy()
    lm = dspy.LM("openai/gpt-4o-mini", cache_in_memory=False)
    dspy.configure(lm=lm)
    
    predictor = create_predictor()
    
    # Check if we have the tile examples file
    import os
    tile_images_file = "examples/groovjones_styles/tile_images_list.json"
    
    if os.path.exists(tile_images_file):
        print(f"📁 Found tile images file: {tile_images_file}")
        
        # Test with just a few images for demonstration
        print("⚡ Running async example generation (limited subset)...")
        start_time = asyncio.get_event_loop().time()
        
        # Note: This would process all images - in a real test you might want to 
        # create a smaller test file with just a few images
        examples = await generate_new_examples_async(
            predictor, 
            tile_images_file, 
            "examples/test_async_examples.jsonl"
        )
        
        end_time = asyncio.get_event_loop().time()
        print(f"⏱️ Async example generation completed in {end_time - start_time:.2f} seconds")
        print(f"📝 Generated {len(examples)} examples")
        
        return examples
    else:
        print(f"❌ Tile images file not found: {tile_images_file}")
        print("Skipping async example generation test")
        return []


async def run_concurrent_tasks():
    """Demonstrate running multiple async tasks concurrently."""
    print("\n🔄 Testing Concurrent Async Tasks")
    print("=" * 50)
    
    # Setup
    setup_dspy()
    lm = dspy.LM("openai/gpt-4o-mini", cache_in_memory=False)
    dspy.configure(lm=lm)
    
    predictor = create_predictor()
    trainset, devset = load_training_data()
    
    # Use very small datasets for concurrent testing
    tiny_devset = devset[:2]
    
    print("🚀 Running multiple evaluations concurrently...")
    start_time = asyncio.get_event_loop().time()
    
    # Run multiple evaluations concurrently
    tasks = [
        evaluate_predictors_async(predictor, None, tiny_devset, num_threads=2, display_table=0),
        evaluate_predictors_async(predictor, None, tiny_devset, num_threads=2, display_table=0),
        evaluate_predictors_async(predictor, None, tiny_devset, num_threads=2, display_table=0)
    ]
    
    results = await asyncio.gather(*tasks)
    
    end_time = asyncio.get_event_loop().time()
    print(f"⏱️ Concurrent evaluations completed in {end_time - start_time:.2f} seconds")
    print(f"📊 Results: {results}")
    
    return results


async def main():
    """Main test function."""
    print("🧪 DSPy Async Pipeline Test Suite")
    print("=" * 60)
    
    try:
        # Test 1: Basic async evaluation
        await test_async_evaluation()
        
        # Test 2: Async optimization
        await test_async_optimization()
        
        # Test 3: Concurrent tasks
        await run_concurrent_tasks()
        
        # Test 4: Async example generation (if files exist)
        await test_async_example_generation()
        
        print("\n✅ All async tests completed successfully!")
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
