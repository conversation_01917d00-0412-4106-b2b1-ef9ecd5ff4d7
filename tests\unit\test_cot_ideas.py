"""
Simple test script for the Chain of Thought idea generator.
"""

import async<PERSON>
from cot_idea_generator import CoTIdeaSystem


def test_cot_idea_generation():
    """Test the CoT idea generation system."""
    print("🧠 Testing Chain of Thought Idea Generator")
    print("=" * 50)
    
    # Initialize system
    system = CoTIdeaSystem()
    
    if not system.generator:
        print("❌ Failed to initialize DSPy")
        return
    
    # Test topics
    test_cases = [
        {
            "topic": "improving AI model performance",
            "context": system.get_template_context("technical"),
            "description": "Technical approach to AI optimization"
        },
        {
            "topic": "launching a new mobile app",
            "context": system.get_template_context("business"),
            "description": "Business strategy for app launch"
        },
        {
            "topic": "reducing team meeting fatigue",
            "context": system.get_template_context("problem_solving"),
            "description": "Problem-solving approach to meeting efficiency"
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n🧪 Test Case {i}: {test_case['description']}")
        print("-" * 40)
        
        try:
            # Generate ideas
            ideas = system.generate_ideas(
                topic=test_case["topic"],
                context=test_case["context"]
            )
            
            # Display results
            system.display_ideas(ideas)
            
            # Save results
            filename = f"test_ideas_{i}_{test_case['topic'].replace(' ', '_')}.json"
            system.save_ideas(ideas, filename)
            
            print(f"\n✅ Test case {i} completed successfully")
            
        except Exception as e:
            print(f"❌ Error in test case {i}: {e}")
            continue
    
    print(f"\n🎉 All test cases completed!")


def interactive_test():
    """Run a single interactive test."""
    print("🧠 Interactive CoT Idea Generator Test")
    print("=" * 40)
    
    system = CoTIdeaSystem()
    
    if not system.generator:
        print("❌ Failed to initialize DSPy")
        return
    
    # Get user input
    topic = input("Enter a topic to generate ideas about: ").strip()
    if not topic:
        print("❌ No topic provided")
        return
    
    # Show template options
    print("\nAvailable templates:")
    templates = list(system.templates.keys())
    for i, template_type in enumerate(templates, 1):
        print(f"   {i}. {template_type.title()}")
    print(f"   {len(templates) + 1}. No template")
    
    choice = input(f"Choose template (1-{len(templates) + 1}): ").strip()
    
    context = ""
    if choice.isdigit():
        choice_num = int(choice)
        if 1 <= choice_num <= len(templates):
            template_type = templates[choice_num - 1]
            context = system.get_template_context(template_type)
            print(f"✅ Using {template_type} template")
    
    try:
        # Generate ideas
        print(f"\n🔄 Generating ideas for: {topic}")
        ideas = system.generate_ideas(topic=topic, context=context)
        
        # Display results
        system.display_ideas(ideas)
        
        # Ask to save
        save_choice = input("\nSave ideas to file? (y/n): ").strip().lower()
        if save_choice == 'y':
            filename = f"ideas_{topic.replace(' ', '_').lower()}.json"
            system.save_ideas(ideas, filename)
        
        print("✅ Interactive test completed!")
        
    except Exception as e:
        print(f"❌ Error generating ideas: {e}")


def main():
    """Main function to choose test type."""
    print("🧠 Chain of Thought Idea Generator - Test Suite")
    print("=" * 55)
    print("Choose test type:")
    print("1. Run automated test cases")
    print("2. Interactive single test")
    print("3. Exit")
    
    choice = input("Enter choice (1-3): ").strip()
    
    if choice == "1":
        test_cot_idea_generation()
    elif choice == "2":
        interactive_test()
    elif choice == "3":
        print("👋 Goodbye!")
    else:
        print("❌ Invalid choice")


if __name__ == "__main__":
    main()
