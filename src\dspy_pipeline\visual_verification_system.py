"""
Visual verification system that compares generated images against original tile images.
This provides a more comprehensive validation of the feedback optimization results.
"""

import asyncio
import json
import os
from datetime import datetime
from pathlib import Path
import dspy
from dspy import Example
from PIL import Image
import numpy as np
from sklearn.metrics.pairwise import cosine_similarity
import cv2

# Import our modules
from main import setup_dspy, create_predictor, optimize_predictor_labeled_fewshot
from utils.data_utils import load_examples
from simple_feedback_test import generate_image_from_style, analyze_generated_image, calculate_style_similarity


class VisualVerificationSystem:
    """System for visual verification of generated images against original tiles."""
    
    def __init__(self):
        self.original_predictor = None
        self.optimized_predictor = None
        self.verification_data = []
        self.cost_per_image = 0.04  # DALL-E 3 cost
    
    def setup(self):
        """Setup DSPy and create original predictor."""
        setup_dspy()
        lm = dspy.LM("openai/gpt-4o-mini", cache_in_memory=False)
        dspy.configure(lm=lm)
        self.original_predictor = create_predictor()
        print("✅ DSPy setup complete")
    
    def extract_image_features(self, image_path: str) -> dict:
        """Extract visual features from an image for comparison."""
        try:
            # Load image
            img = cv2.imread(image_path)
            if img is None:
                return {"error": f"Could not load image: {image_path}"}
            
            # Convert to RGB
            img_rgb = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
            
            # Extract basic features
            features = {
                "dimensions": img_rgb.shape[:2],
                "mean_color": np.mean(img_rgb, axis=(0, 1)).tolist(),
                "color_std": np.std(img_rgb, axis=(0, 1)).tolist(),
                "brightness": np.mean(img_rgb),
                "contrast": np.std(img_rgb)
            }
            
            # Color histogram
            hist_r = cv2.calcHist([img_rgb], [0], None, [32], [0, 256])
            hist_g = cv2.calcHist([img_rgb], [1], None, [32], [0, 256])
            hist_b = cv2.calcHist([img_rgb], [2], None, [32], [0, 256])
            
            features["color_histogram"] = {
                "red": hist_r.flatten().tolist(),
                "green": hist_g.flatten().tolist(),
                "blue": hist_b.flatten().tolist()
            }
            
            return features
            
        except Exception as e:
            return {"error": f"Error extracting features: {e}"}
    
    def calculate_visual_similarity(self, features1: dict, features2: dict) -> float:
        """Calculate visual similarity between two sets of image features."""
        if "error" in features1 or "error" in features2:
            return 0.0
        
        try:
            # Color similarity (mean color)
            color_sim = 1.0 - np.linalg.norm(
                np.array(features1["mean_color"]) - np.array(features2["mean_color"])
            ) / (255 * np.sqrt(3))
            
            # Histogram similarity
            hist1_r = np.array(features1["color_histogram"]["red"])
            hist1_g = np.array(features1["color_histogram"]["green"])
            hist1_b = np.array(features1["color_histogram"]["blue"])
            
            hist2_r = np.array(features2["color_histogram"]["red"])
            hist2_g = np.array(features2["color_histogram"]["green"])
            hist2_b = np.array(features2["color_histogram"]["blue"])
            
            # Normalize histograms
            hist1_r = hist1_r / (np.sum(hist1_r) + 1e-8)
            hist1_g = hist1_g / (np.sum(hist1_g) + 1e-8)
            hist1_b = hist1_b / (np.sum(hist1_b) + 1e-8)
            
            hist2_r = hist2_r / (np.sum(hist2_r) + 1e-8)
            hist2_g = hist2_g / (np.sum(hist2_g) + 1e-8)
            hist2_b = hist2_b / (np.sum(hist2_b) + 1e-8)
            
            # Calculate histogram correlation
            hist_sim_r = np.corrcoef(hist1_r, hist2_r)[0, 1]
            hist_sim_g = np.corrcoef(hist1_g, hist2_g)[0, 1]
            hist_sim_b = np.corrcoef(hist1_b, hist2_b)[0, 1]
            
            # Handle NaN values
            hist_sim_r = 0.0 if np.isnan(hist_sim_r) else max(0.0, hist_sim_r)
            hist_sim_g = 0.0 if np.isnan(hist_sim_g) else max(0.0, hist_sim_g)
            hist_sim_b = 0.0 if np.isnan(hist_sim_b) else max(0.0, hist_sim_b)
            
            hist_sim = (hist_sim_r + hist_sim_g + hist_sim_b) / 3
            
            # Brightness and contrast similarity
            brightness_sim = 1.0 - abs(features1["brightness"] - features2["brightness"]) / 255
            contrast_sim = 1.0 - abs(features1["contrast"] - features2["contrast"]) / 255
            
            # Combined similarity score
            visual_similarity = (
                color_sim * 0.3 +
                hist_sim * 0.4 +
                brightness_sim * 0.15 +
                contrast_sim * 0.15
            )
            
            return max(0.0, min(1.0, visual_similarity))
            
        except Exception as e:
            print(f"Error calculating visual similarity: {e}")
            return 0.0
    
    async def run_visual_verification_test(self, num_examples: int = 5):
        """Run comprehensive visual verification test."""
        print("🔍 Visual Verification System")
        print("=" * 60)
        print(f"💰 Estimated cost: ${num_examples * self.cost_per_image:.2f} for {num_examples} images")
        print("🎯 This test compares generated images against original tile images")
        
        # Setup
        self.setup()
        
        # Load examples
        print("\n1️⃣ Loading tile examples...")
        examples_file = "examples/groovjones_styles/tile_examples.jsonl"
        all_examples = load_examples(examples_file)
        
        # Select diverse examples
        selected_examples = []
        used_styles = set()
        
        for example in all_examples:
            if len(selected_examples) >= num_examples:
                break
            try:
                metadata = json.loads(example["metadata"])
                style_name = metadata.get("style_name", "unknown")
                if style_name not in used_styles:
                    selected_examples.append(example)
                    used_styles.add(style_name)
            except:
                continue
        
        print(f"✅ Selected {len(selected_examples)} examples from {len(used_styles)} different styles")
        
        # Create output directory
        output_dir = "visual_verification_test"
        os.makedirs(output_dir, exist_ok=True)
        
        # Process each example
        print("\n2️⃣ Processing examples with visual verification...")
        verification_results = []
        
        for i, example in enumerate(selected_examples, 1):
            try:
                print(f"\n--- Processing Example {i}/{len(selected_examples)} ---")
                
                # Extract data
                original_instruction = example["instruction"]
                expected_style = example["style"]
                expected_metadata = example["metadata"]
                
                # Get original tile image path
                original_tile_path = original_instruction.replace("Describe the image: ", "")
                
                # Get style name
                metadata = json.loads(expected_metadata)
                style_name = metadata.get("style_name", f"style_{i}").replace(" ", "_")
                
                print(f"🎨 Style: {style_name}")
                print(f"📁 Original tile: {Path(original_tile_path).name}")
                
                # Check if original tile exists
                if not os.path.exists(original_tile_path):
                    print(f"❌ Original tile not found: {original_tile_path}")
                    continue
                
                # Generate image from style analysis
                timestamp = datetime.now().strftime("%H%M%S")
                generated_image_path = os.path.join(
                    output_dir, 
                    f"generated_{style_name}_{i:02d}_{timestamp}.png"
                )
                
                await generate_image_from_style(expected_style, generated_image_path)
                
                # Extract visual features from both images
                print("🔍 Extracting visual features...")
                original_features = self.extract_image_features(original_tile_path)
                generated_features = self.extract_image_features(generated_image_path)
                
                # Calculate visual similarity
                visual_similarity = self.calculate_visual_similarity(original_features, generated_features)
                print(f"👁️  Visual similarity: {visual_similarity:.3f}")
                
                # Analyze generated image with predictor
                print("🤖 Analyzing with DSPy predictor...")
                generated_analysis = await analyze_generated_image(self.original_predictor, generated_image_path)
                
                # Calculate style analysis similarity
                style_similarity = calculate_style_similarity(expected_style, generated_analysis["style"])
                print(f"📊 Style similarity: {style_similarity['overall_score']:.3f}")
                
                # Store comprehensive results
                verification_item = {
                    "index": i,
                    "style_name": style_name,
                    "original_tile_path": original_tile_path,
                    "generated_image_path": generated_image_path,
                    "expected_style": expected_style,
                    "generated_style_analysis": generated_analysis["style"],
                    "visual_similarity": visual_similarity,
                    "style_similarity": style_similarity["overall_score"],
                    "original_features": original_features,
                    "generated_features": generated_features,
                    "combined_score": (visual_similarity + style_similarity["overall_score"]) / 2
                }
                
                verification_results.append(verification_item)
                self.verification_data.append(verification_item)
                
                print(f"🎯 Combined score: {verification_item['combined_score']:.3f}")
                
                # Show running averages
                if verification_results:
                    avg_visual = sum(item["visual_similarity"] for item in verification_results) / len(verification_results)
                    avg_style = sum(item["style_similarity"] for item in verification_results) / len(verification_results)
                    avg_combined = sum(item["combined_score"] for item in verification_results) / len(verification_results)
                    
                    print(f"📈 Running averages:")
                    print(f"   👁️  Visual: {avg_visual:.3f}")
                    print(f"   📊 Style: {avg_style:.3f}")
                    print(f"   🎯 Combined: {avg_combined:.3f}")
                
            except Exception as e:
                print(f"❌ Error processing example {i}: {e}")
                continue
        
        if not verification_results:
            print("❌ No verification results generated")
            return
        
        # Calculate comprehensive statistics
        print(f"\n3️⃣ Comprehensive Results Analysis:")
        
        visual_scores = [item["visual_similarity"] for item in verification_results]
        style_scores = [item["style_similarity"] for item in verification_results]
        combined_scores = [item["combined_score"] for item in verification_results]
        
        print(f"\n📊 Visual Similarity Analysis:")
        print(f"   Average: {np.mean(visual_scores):.3f}")
        print(f"   Std Dev: {np.std(visual_scores):.3f}")
        print(f"   Range: {min(visual_scores):.3f} - {max(visual_scores):.3f}")
        
        print(f"\n📊 Style Analysis Similarity:")
        print(f"   Average: {np.mean(style_scores):.3f}")
        print(f"   Std Dev: {np.std(style_scores):.3f}")
        print(f"   Range: {min(style_scores):.3f} - {max(style_scores):.3f}")
        
        print(f"\n🎯 Combined Score Analysis:")
        print(f"   Average: {np.mean(combined_scores):.3f}")
        print(f"   Std Dev: {np.std(combined_scores):.3f}")
        print(f"   Range: {min(combined_scores):.3f} - {max(combined_scores):.3f}")
        
        # Show individual results
        print(f"\n📋 Individual Verification Results:")
        for result in verification_results:
            print(f"   {result['style_name']}: Visual={result['visual_similarity']:.3f}, Style={result['style_similarity']:.3f}, Combined={result['combined_score']:.3f}")
        
        # Save comprehensive results
        results_file = os.path.join(output_dir, f"visual_verification_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json")
        results = {
            "configuration": {
                "num_examples": len(verification_results),
                "test_type": "visual_verification",
                "comparison_method": "original_tiles_vs_generated"
            },
            "verification_data": verification_results,
            "statistics": {
                "visual_similarity": {
                    "mean": float(np.mean(visual_scores)),
                    "std": float(np.std(visual_scores)),
                    "min": float(min(visual_scores)),
                    "max": float(max(visual_scores))
                },
                "style_similarity": {
                    "mean": float(np.mean(style_scores)),
                    "std": float(np.std(style_scores)),
                    "min": float(min(style_scores)),
                    "max": float(max(style_scores))
                },
                "combined_score": {
                    "mean": float(np.mean(combined_scores)),
                    "std": float(np.std(combined_scores)),
                    "min": float(min(combined_scores)),
                    "max": float(max(combined_scores))
                }
            },
            "timestamp": datetime.now().isoformat()
        }
        
        with open(results_file, 'w') as f:
            json.dump(results, f, indent=2)
        
        print(f"\n📁 Results saved to: {results_file}")
        print("✅ Visual verification test completed!")
        
        return results


async def main():
    """Main function to run visual verification test."""
    print("🔍 Visual Verification System")
    print("Choose the number of examples to test:")
    print("1. 5 examples (~$0.20)")
    print("2. 10 examples (~$0.40)")
    print("3. Custom number")
    
    choice = input("Enter choice (1-3): ").strip()
    
    if choice == "1":
        num_examples = 5
    elif choice == "2":
        num_examples = 10
    elif choice == "3":
        try:
            num_examples = int(input("Enter number of examples: ").strip())
            if num_examples < 1 or num_examples > 20:
                print("❌ Please enter a number between 1 and 20")
                return
        except ValueError:
            print("❌ Invalid number")
            return
    else:
        print("❌ Invalid choice")
        return
    
    system = VisualVerificationSystem()
    await system.run_visual_verification_test(num_examples)


if __name__ == "__main__":
    asyncio.run(main())
