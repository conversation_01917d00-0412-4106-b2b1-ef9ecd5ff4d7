"""
Simple runner for the Flask app with error handling.
"""

try:
    print("🚀 Starting Chain of Thought Idea Generator Web App")
    print("=" * 50)
    
    from app import app, initialize_system
    
    print("📦 Flask app imported successfully")
    
    # Initialize the system
    if initialize_system():
        print("🌐 Starting Flask server on http://localhost:5000")
        app.run(debug=True, host='0.0.0.0', port=5000)
    else:
        print("❌ Cannot start server - system initialization failed")
        
except Exception as e:
    print(f"❌ Error starting application: {e}")
    import traceback
    traceback.print_exc()
