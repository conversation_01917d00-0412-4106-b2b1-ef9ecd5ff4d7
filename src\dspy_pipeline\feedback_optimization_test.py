"""
Feedback optimization test script.
Takes generated style examples, creates images from them, analyzes the results,
and uses the feedback for DSPy optimization.

This script tests with only 2 images to minimize costs.
"""

import asyncio
import json
import os
import requests
from datetime import datetime
from openai import OpenAI
import dspy
from dspy import Example

# Import our modules
from main import setup_dspy, create_predictor, load_training_data
from utils.data_utils import load_examples
from models.style_extraction import StyleAnalysis


class ImageGenerationSignature(dspy.Signature):
    """Generate an image prompt from a style analysis."""
    style_analysis = dspy.InputField(desc="JSON style analysis from DSPy")
    image_prompt = dspy.OutputField(desc="DALL-E image generation prompt")


class FeedbackAnalysisSignature(dspy.Signature):
    """Analyze feedback between expected and actual style analysis."""
    expected_style = dspy.InputField(desc="Expected style analysis JSON")
    actual_style = dspy.InputField(desc="Actual style analysis JSON from generated image")
    feedback_score = dspy.OutputField(desc="Similarity score (0-1) and improvement suggestions")


class ImageGenerator:
    """Handles DALL-E image generation."""
    
    def __init__(self):
        self.client = OpenAI()
    
    def generate_image_from_style(self, style_analysis_json: str, output_path: str) -> str:
        """Generate an image using DALL-E based on style analysis."""
        try:
            # Parse the style analysis
            style_data = json.loads(style_analysis_json)
            
            # Extract key visual elements for prompt
            core_chars = style_data.get("core_characteristics", {})
            
            # Build a comprehensive prompt
            prompt_parts = []
            
            # Visual composition
            if "visual_composition" in core_chars:
                prompt_parts.append(core_chars["visual_composition"])
            
            # Color characteristics
            if "color_characteristics" in core_chars:
                prompt_parts.append(core_chars["color_characteristics"])
            
            # Mark making and technique
            if "mark_making_and_technique" in core_chars:
                prompt_parts.append(core_chars["mark_making_and_technique"])
            
            # Form and structure
            if "form_and_structure" in core_chars:
                prompt_parts.append(core_chars["form_and_structure"])
            
            # Add a simple subject (bear head as in original examples)
            prompt_parts.append("Create an abstract bear head")
            
            # Combine into final prompt
            full_prompt = ". ".join(prompt_parts)
            
            # Limit prompt length for DALL-E
            if len(full_prompt) > 1000:
                full_prompt = full_prompt[:1000] + "..."
            
            print(f"🎨 Generating image with prompt: {full_prompt[:200]}...")
            
            # Generate image
            response = self.client.images.generate(
                model="dall-e-3",
                prompt=full_prompt,
                size="1024x1024",
                quality="standard",
                n=1,
            )
            
            # Download and save the image
            image_url = response.data[0].url
            image_response = requests.get(image_url)
            
            if image_response.status_code == 200:
                with open(output_path, 'wb') as f:
                    f.write(image_response.content)
                print(f"✅ Image saved to: {output_path}")
                return output_path
            else:
                raise Exception(f"Failed to download image: {image_response.status_code}")
                
        except Exception as e:
            print(f"❌ Error generating image: {e}")
            raise


class FeedbackOptimizer:
    """Handles the feedback optimization process."""
    
    def __init__(self):
        self.predictor = None
        self.image_generator = ImageGenerator()
        self.feedback_examples = []
    
    def setup(self):
        """Setup DSPy and predictor."""
        setup_dspy()
        lm = dspy.LM("openai/gpt-4o-mini", cache_in_memory=False)
        dspy.configure(lm=lm)
        self.predictor = create_predictor()
    
    async def analyze_image_async(self, image_path: str) -> dict:
        """Analyze an image using our DSPy predictor asynchronously."""
        instruction = f"Describe the image: {image_path}"
        
        # Run predictor in thread pool
        loop = asyncio.get_event_loop()
        response = await loop.run_in_executor(
            None, 
            lambda: self.predictor(instruction=instruction)
        )
        
        return {
            "style": response.style,
            "metadata": response.metadata
        }
    
    def calculate_feedback_score(self, expected_style: str, actual_style: str) -> dict:
        """Calculate similarity score between expected and actual style analysis."""
        try:
            expected_data = json.loads(expected_style)
            actual_data = json.loads(actual_style)
            
            # Simple scoring based on key characteristics
            score_components = []
            
            # Compare core characteristics
            expected_core = expected_data.get("core_characteristics", {})
            actual_core = actual_data.get("core_characteristics", {})
            
            key_fields = [
                "visual_composition", "color_characteristics", 
                "mark_making_and_technique", "form_and_structure"
            ]
            
            for field in key_fields:
                if field in expected_core and field in actual_core:
                    # Simple keyword overlap scoring
                    expected_words = set(expected_core[field].lower().split())
                    actual_words = set(actual_core[field].lower().split())
                    
                    if expected_words and actual_words:
                        overlap = len(expected_words.intersection(actual_words))
                        total = len(expected_words.union(actual_words))
                        field_score = overlap / total if total > 0 else 0
                        score_components.append(field_score)
            
            overall_score = sum(score_components) / len(score_components) if score_components else 0
            
            return {
                "overall_score": overall_score,
                "component_scores": dict(zip(key_fields[:len(score_components)], score_components)),
                "feedback": f"Overall similarity: {overall_score:.2f}. " + 
                          ("Good match!" if overall_score > 0.3 else "Significant differences detected.")
            }
            
        except Exception as e:
            return {
                "overall_score": 0.0,
                "component_scores": {},
                "feedback": f"Error calculating score: {e}"
            }
    
    async def process_feedback_example(self, example_data: dict, output_dir: str) -> dict:
        """Process a single example through the feedback loop."""
        try:
            # Extract data
            original_instruction = example_data["instruction"]
            expected_style = example_data["style"]
            expected_metadata = example_data["metadata"]
            
            # Get style name for file naming
            metadata = json.loads(expected_metadata)
            style_name = metadata.get("style_name", "unknown_style").replace(" ", "_")
            
            # Generate image from style analysis
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            generated_image_path = os.path.join(output_dir, f"generated_{style_name}_{timestamp}.png")
            
            print(f"\n🔄 Processing: {style_name}")
            print(f"📝 Original instruction: {original_instruction}")
            
            # Generate image
            self.image_generator.generate_image_from_style(expected_style, generated_image_path)
            
            # Analyze the generated image
            print(f"🔍 Analyzing generated image...")
            actual_analysis = await self.analyze_image_async(generated_image_path)
            
            # Calculate feedback score
            feedback_score = self.calculate_feedback_score(expected_style, actual_analysis["style"])
            
            # Create feedback example for optimization
            feedback_example = {
                "original_instruction": original_instruction,
                "expected_style": expected_style,
                "expected_metadata": expected_metadata,
                "generated_image_path": generated_image_path,
                "actual_style": actual_analysis["style"],
                "actual_metadata": actual_analysis["metadata"],
                "feedback_score": feedback_score,
                "timestamp": timestamp
            }
            
            print(f"📊 Feedback Score: {feedback_score['overall_score']:.3f}")
            print(f"💬 Feedback: {feedback_score['feedback']}")
            
            return feedback_example
            
        except Exception as e:
            print(f"❌ Error processing example: {e}")
            return None
    
    async def run_feedback_test(self, num_examples: int = 2):
        """Run the complete feedback optimization test."""
        print("🚀 Starting Feedback Optimization Test")
        print("=" * 60)
        
        # Setup
        self.setup()
        
        # Create output directory
        output_dir = "feedback_test_results"
        os.makedirs(output_dir, exist_ok=True)
        
        # Load examples
        examples_file = "examples/groovjones_styles/tile_examples.jsonl"
        if not os.path.exists(examples_file):
            print(f"❌ Examples file not found: {examples_file}")
            return
        
        examples = load_examples(examples_file)
        print(f"📚 Loaded {len(examples)} examples")
        
        # Take first N examples for testing
        test_examples = examples[:num_examples]
        print(f"🧪 Testing with {len(test_examples)} examples")
        
        # Process each example
        feedback_results = []
        for i, example in enumerate(test_examples):
            print(f"\n{'='*20} Example {i+1}/{len(test_examples)} {'='*20}")
            
            result = await self.process_feedback_example(example, output_dir)
            if result:
                feedback_results.append(result)
                self.feedback_examples.append(result)
        
        # Save results
        results_file = os.path.join(output_dir, f"feedback_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json")
        with open(results_file, 'w') as f:
            json.dump(feedback_results, f, indent=2)
        
        print(f"\n📁 Results saved to: {results_file}")
        
        # Summary
        if feedback_results:
            avg_score = sum(r["feedback_score"]["overall_score"] for r in feedback_results) / len(feedback_results)
            print(f"\n📈 Average Feedback Score: {avg_score:.3f}")
            
            print(f"\n🎯 Individual Scores:")
            for i, result in enumerate(feedback_results):
                score = result["feedback_score"]["overall_score"]
                style_name = json.loads(result["expected_metadata"])["style_name"]
                print(f"  {i+1}. {style_name}: {score:.3f}")
        
        return feedback_results
    
    def create_optimization_examples(self) -> list:
        """Create DSPy examples from feedback for optimization."""
        optimization_examples = []
        
        for feedback in self.feedback_examples:
            # Create examples that teach the model to generate better style analyses
            # that would result in images closer to the original
            
            example = Example(
                instruction=feedback["original_instruction"],
                style=feedback["expected_style"],  # The target we want to achieve
                metadata=feedback["expected_metadata"]
            ).with_inputs('instruction')
            
            optimization_examples.append(example)
        
        return optimization_examples


async def main():
    """Main function to run the feedback optimization test."""
    optimizer = FeedbackOptimizer()
    
    try:
        # Run feedback test with 2 examples
        results = await optimizer.run_feedback_test(num_examples=2)
        
        if results:
            print(f"\n✅ Feedback test completed successfully!")
            print(f"📊 Processed {len(results)} examples")
            
            # Create optimization examples
            opt_examples = optimizer.create_optimization_examples()
            print(f"🎯 Created {len(opt_examples)} optimization examples")
            
            # Here you could use these examples for further DSPy optimization
            # For example:
            # from main import optimize_predictor_labeled_fewshot
            # optimized_predictor = optimize_predictor_labeled_fewshot(
            #     optimizer.predictor, opt_examples, k=len(opt_examples)
            # )
            
        else:
            print("❌ No results generated")
            
    except Exception as e:
        print(f"❌ Error in main: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
