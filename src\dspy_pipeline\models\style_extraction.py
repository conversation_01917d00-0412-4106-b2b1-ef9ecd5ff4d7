"""
Pydantic models for style extraction and analysis.
"""

import statistics
from pydantic import BaseModel, Field, model_validator
from typing import List


class CoreCharacteristics(BaseModel):
    visual_composition: str = Field(..., description="Principles of element arrangement, spatial balance, and focal structure.")
    color_characteristics: str = Field(..., description="Abstract color relationships, contrast strategies, and application styles.")
    color_palette: list[dict[str, int]] = Field(..., description="Reduced color palette to 16 most used. keep colorcodes: str, percentage: int.")
    mark_making_and_technique: str = Field(..., description="Stroke behavior, texture visibility, and consistency of technique.")
    form_and_structure: str = Field(..., description="Shape abstraction, dimensional handling, and structural tendencies.")
    light_and_shadow: str = Field(..., description="Light source abstraction, contrast, and shadow treatment.")
    subject_matter_and_content: str = Field(..., description="Level of abstraction, representational approach, and narrative tone.")
    material_and_medium: str = Field(..., description="Surface interaction, transparency, texture behaviors of the medium.")
    spatial_treatment: str = Field(..., description="Depth cues, scale handling, and visual space strategies.")
    temporal_aspects: str = Field(..., description="Motion implication, gesture abstraction, and rhythmic patterns.")
    conceptual_framework: str = Field(..., description="Underlying philosophical/artistic approach implied by style.")


class ExclusionCriteria(BaseModel):
    technical_exclusions: str = Field(..., description="Techniques that must be avoided to maintain stylistic coherence.")
    aesthetic_exclusions: str = Field(..., description="Visual features or qualities that are stylistically incompatible.")
    content_exclusions: str = Field(..., description="Concrete visual elements or subjects that must not be referenced.")
    conceptual_exclusions: str = Field(..., description="Conceptual or thematic directions that contradict the intended aesthetic.")


class FlexibilityParameters(BaseModel):
    rigid_elements: List[str] = Field(..., description="Non-negotiable style components that define core identity.")
    flexible_elements: List[str] = Field(..., description="Aspects that can vary without breaking stylistic intent.")
    interpretive_range: List[str] = Field(..., description="Acceptable scope of variation or adaptation.")


class VerificationPoint(BaseModel):
    score: float = Field(..., ge=0.0, le=1.0, description="Fractional score for this verification category")
    positives: List[str] = Field(default_factory=list, description="Supporting observations that increased the score")
    negatives: List[str] = Field(default_factory=list, description="Violations or weaknesses that reduced the score")


class VerificationChecks(BaseModel):
    universal_applicability: VerificationPoint = Field(..., description="Style applies to any subject (portrait, abstract, still life, etc.)")
    abstraction_verification: VerificationPoint = Field(..., description="Avoids scene-specific or concrete terms; uses abstract principles")
    content_elimination: VerificationPoint = Field(..., description="Removes all references to specific objects, scenes, or materials")
    principle_focus: VerificationPoint = Field(..., description="Describes how visual principles are applied rather than what is shown")
    overall_score: float = Field(default=0.0, description="Aggregate mean of all verification scores")

    @model_validator(mode="after")
    def compute_score(cls, instance):
        fields = [
            instance.universal_applicability.score,
            instance.abstraction_verification.score,
            instance.content_elimination.score,
            instance.principle_focus.score
        ]
        instance.overall_score = statistics.mean(fields)
        return instance


class StyleAnalysis(BaseModel):
    core_characteristics: CoreCharacteristics
    exclusion_criteria: ExclusionCriteria
    priority_hierarchy: List[str] = Field(..., description="Ordered list of defining style elements from most to least critical.")
    flexibility_parameters: FlexibilityParameters


class StyleAnalysisMetadata(BaseModel):
    style_name: str = Field(..., description="Abstract label representing the extracted style identity.")
    labels: list[str] = Field(..., description="Labels for indentifying the style.")
    verification_checks: VerificationChecks
